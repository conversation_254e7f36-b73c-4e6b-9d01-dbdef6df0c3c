<?php
/**
 * Uninstall Glowess City E-commerce
 * 
 * This file is executed when the plugin is uninstalled
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Delete plugin data
 */
function glowess_city_ecommerce_uninstall() {
    global $wpdb;
    
    // Delete cities and their meta
    $cities = get_posts(array(
        'post_type' => 'city',
        'posts_per_page' => -1,
        'post_status' => 'any'
    ));
    
    foreach ($cities as $city) {
        wp_delete_post($city->ID, true);
    }
    
    // Delete product city meta
    $wpdb->delete(
        $wpdb->postmeta,
        array('meta_key' => '_product_city'),
        array('%s')
    );
    
    // Delete category city meta
    $wpdb->delete(
        $wpdb->termmeta,
        array('meta_key' => 'category_city'),
        array('%s')
    );
    
    // Delete city meta
    $city_meta_keys = array(
        '_city_gallery_images',
        '_city_meta_title',
        '_city_meta_description',
        '_city_meta_keywords',
        '_city_featured',
        '_city_color',
        '_city_show_in_homepage',
        '_city_order'
    );
    
    foreach ($city_meta_keys as $meta_key) {
        $wpdb->delete(
            $wpdb->postmeta,
            array('meta_key' => $meta_key),
            array('%s')
        );
    }
    
    // Delete plugin options
    delete_option('glowess_city_ecommerce_settings');
    delete_option('glowess_city_ecommerce_setup_complete');
    delete_option('glowess_city_ecommerce_version');
    
    // Delete transients
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_glowess_city_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_glowess_city_%'");
    
    // Clear any cached data
    wp_cache_flush();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}

// Only run uninstall if user has proper permissions
if (current_user_can('activate_plugins')) {
    glowess_city_ecommerce_uninstall();
}
