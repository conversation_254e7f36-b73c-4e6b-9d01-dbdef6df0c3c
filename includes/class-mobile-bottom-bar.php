<?php
/**
 * Mobile Bottom Bar
 * Mobil cihazlarda alt navigasyon çubuğu
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_Mobile_Bottom_Bar {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Admin ayarları
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        
        // Frontend'de bottom bar göster
        add_action('wp_footer', array($this, 'display_mobile_bottom_bar'));
        
        // CSS ve JS ekle
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Admin menüsü ekle
     */
    public function add_admin_menu() {
        add_submenu_page(
            'edit.php?post_type=city',
            __('Mobile Bottom Bar', 'glowess-city-ecommerce'),
            __('Mobile Bottom Bar', 'glowess-city-ecommerce'),
            'manage_options',
            'mobile-bottom-bar',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Ayarları kaydet
     */
    public function register_settings() {
        register_setting('mobile_bottom_bar_settings', 'mobile_bottom_bar_options');
    }
    
    /**
     * Admin sayfası
     */
    public function admin_page() {
        if (isset($_POST['submit'])) {
            $options = array(
                'enabled' => isset($_POST['enabled']) ? 1 : 0,
                'home_text' => sanitize_text_field($_POST['home_text']),
                'home_url' => esc_url_raw($_POST['home_url']),
                'categories_text' => sanitize_text_field($_POST['categories_text']),
                'categories_url' => esc_url_raw($_POST['categories_url']),
                'cart_text' => sanitize_text_field($_POST['cart_text']),
                'cart_url' => esc_url_raw($_POST['cart_url']),
                'orders_text' => sanitize_text_field($_POST['orders_text']),
                'orders_url' => esc_url_raw($_POST['orders_url']),
                'excluded_pages' => sanitize_textarea_field($_POST['excluded_pages'])
            );
            
            update_option('mobile_bottom_bar_options', $options);
            echo '<div class="notice notice-success"><p>' . __('Ayarlar kaydedildi!', 'glowess-city-ecommerce') . '</p></div>';
        }
        
        $options = get_option('mobile_bottom_bar_options', array(
            'enabled' => 1,
            'home_text' => 'Ana Sayfa',
            'home_url' => home_url(),
            'categories_text' => 'Kategoriler',
            'categories_url' => wc_get_page_permalink('shop'),
            'cart_text' => 'Sepet',
            'cart_url' => wc_get_cart_url(),
            'orders_text' => 'Sipariş Takip',
            'orders_url' => wc_get_account_endpoint_url('orders'),
            'excluded_pages' => 'cart,checkout'
        ));
        
        ?>
        <div class="wrap">
            <h1><?php _e('Mobile Bottom Bar Ayarları', 'glowess-city-ecommerce'); ?></h1>
            
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Aktif', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <input type="checkbox" name="enabled" value="1" <?php checked($options['enabled'], 1); ?> />
                            <label><?php _e('Mobile Bottom Bar\'ı aktif et', 'glowess-city-ecommerce'); ?></label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Ana Sayfa', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <input type="text" name="home_text" value="<?php echo esc_attr($options['home_text']); ?>" class="regular-text" placeholder="Ana Sayfa" />
                            <br><br>
                            <input type="url" name="home_url" value="<?php echo esc_attr($options['home_url']); ?>" class="regular-text" placeholder="<?php echo home_url(); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Kategoriler', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <input type="text" name="categories_text" value="<?php echo esc_attr($options['categories_text']); ?>" class="regular-text" placeholder="Kategoriler" />
                            <br><br>
                            <input type="url" name="categories_url" value="<?php echo esc_attr($options['categories_url']); ?>" class="regular-text" placeholder="<?php echo wc_get_page_permalink('shop'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sepet', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <input type="text" name="cart_text" value="<?php echo esc_attr($options['cart_text']); ?>" class="regular-text" placeholder="Sepet" />
                            <br><br>
                            <input type="url" name="cart_url" value="<?php echo esc_attr($options['cart_url']); ?>" class="regular-text" placeholder="<?php echo wc_get_cart_url(); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sipariş Takip', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <input type="text" name="orders_text" value="<?php echo esc_attr($options['orders_text']); ?>" class="regular-text" placeholder="Sipariş Takip" />
                            <br><br>
                            <input type="url" name="orders_url" value="<?php echo esc_attr($options['orders_url']); ?>" class="regular-text" placeholder="<?php echo wc_get_account_endpoint_url('orders'); ?>" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Hariç Tutulan Sayfalar', 'glowess-city-ecommerce'); ?></th>
                        <td>
                            <textarea name="excluded_pages" rows="3" class="large-text"><?php echo esc_textarea($options['excluded_pages']); ?></textarea>
                            <p class="description"><?php _e('Bottom bar\'ın gösterilmeyeceği sayfa slug\'ları (virgülle ayırın). Örn: cart,checkout,account', 'glowess-city-ecommerce'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * CSS ve JS yükle
     */
    public function enqueue_scripts() {
        if (!$this->should_show_bottom_bar()) {
            return;
        }

        // Font Awesome yükle
        wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

        wp_add_inline_style('wp-block-library', $this->get_bottom_bar_css());
    }
    
    /**
     * Bottom bar gösterilmeli mi kontrol et
     */
    private function should_show_bottom_bar() {
        $options = get_option('mobile_bottom_bar_options', array('enabled' => 1));
        
        if (empty($options['enabled'])) {
            return false;
        }
        
        // Hariç tutulan sayfaları kontrol et
        $excluded_pages = isset($options['excluded_pages']) ? $options['excluded_pages'] : 'cart,checkout';
        $excluded_array = array_map('trim', explode(',', $excluded_pages));
        
        foreach ($excluded_array as $excluded) {
            if (empty($excluded)) continue;
            
            if (is_page($excluded) || is_cart() && $excluded === 'cart' || is_checkout() && $excluded === 'checkout') {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Mobile bottom bar göster
     */
    public function display_mobile_bottom_bar() {
        if (!$this->should_show_bottom_bar()) {
            return;
        }
        
        $options = get_option('mobile_bottom_bar_options', array(
            'home_text' => 'Ana Sayfa',
            'home_url' => home_url(),
            'categories_text' => 'Kategoriler',
            'categories_url' => wc_get_page_permalink('shop'),
            'cart_text' => 'Sepet',
            'cart_url' => wc_get_cart_url(),
            'orders_text' => 'Sipariş Takip',
            'orders_url' => wc_get_account_endpoint_url('orders')
        ));
        
        // City parametresini korumak için URL'lere ekle
        $city_param = '';
        if (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_param = '?city=' . sanitize_text_field($_GET['city']);
        }
        
        ?>
        <div class="mobile-bottom-bar">
            <a href="<?php echo esc_url($options['home_url'] . $city_param); ?>" class="bottom-bar-item">
                <i class="fas fa-home icon"></i>
                <span class="text"><?php echo esc_html($options['home_text']); ?></span>
            </a>

            <a href="<?php echo esc_url($options['categories_url'] . $city_param); ?>" class="bottom-bar-item">
                <i class="fas fa-th-large icon"></i>
                <span class="text"><?php echo esc_html($options['categories_text']); ?></span>
            </a>

            <a href="<?php echo esc_url($options['cart_url']); ?>" class="bottom-bar-item cart-item">
                <i class="fas fa-shopping-cart icon"></i>
                <span class="text"><?php echo esc_html($options['cart_text']); ?></span>
                <?php if (WC()->cart && WC()->cart->get_cart_contents_count() > 0): ?>
                    <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                <?php endif; ?>
            </a>

            <a href="<?php echo esc_url($options['orders_url']); ?>" class="bottom-bar-item">
                <i class="fas fa-box icon"></i>
                <span class="text"><?php echo esc_html($options['orders_text']); ?></span>
            </a>
        </div>
        <?php
    }
    
    /**
     * Bottom bar CSS'i
     */
    private function get_bottom_bar_css() {
        return '
        .mobile-bottom-bar {
            display: none !important;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #e0e0e0;
            padding: 8px 0;
            z-index: 999999;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            justify-content: space-around;
            align-items: center;
        }
        
        .mobile-bottom-bar .bottom-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            min-width: 60px;
        }
        
        .mobile-bottom-bar .bottom-bar-item:hover,
        .mobile-bottom-bar .bottom-bar-item:active {
            color: #fff !important;
            background: #000 !important;
        }
        
        .mobile-bottom-bar .bottom-bar-item .icon {
            font-size: 18px;
            margin-bottom: 4px;
            display: block;
        }
        
        .mobile-bottom-bar .bottom-bar-item .text {
            font-size: 11px;
            font-weight: 500;
            text-align: center;
        }
        
        .mobile-bottom-bar .cart-item .cart-count {
            position: absolute;
            top: -2px;
            right: 8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        /* Masaüstünde kesinlikle gizle */
        @media (min-width: 769px) {
            .mobile-bottom-bar {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
        }

        /* Sadece mobilde göster */
        @media (max-width: 768px) {
            .mobile-bottom-bar {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            body {
                padding-bottom: 70px !important;
            }
        }

        /* Tablet ve üstü için ekstra gizleme */
        @media screen and (min-width: 769px) {
            body .mobile-bottom-bar,
            html .mobile-bottom-bar,
            .mobile-bottom-bar {
                display: none !important;
            }
        }
        ';
    }
}
