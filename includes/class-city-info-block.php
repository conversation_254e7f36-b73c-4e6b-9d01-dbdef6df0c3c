<?php
/**
 * City Info Gutenberg Block Class
 * 
 * Handles the registration and rendering of the city info block
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Info_Block {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_block'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_block_editor_assets'));
        add_action('wp_ajax_get_cities_list', array($this, 'get_cities_list'));
        add_action('wp_ajax_nopriv_get_cities_list', array($this, 'get_cities_list'));
    }
    
    /**
     * Register the block
     */
    public function register_block() {
        // Register block
        register_block_type('glowess/city-info', array(
            'editor_script' => 'glowess-city-info-block',
            'render_callback' => array($this, 'render_block'),
            'attributes' => array(
                'citySlug' => array(
                    'type' => 'string',
                    'default' => ''
                ),
                'showTitle' => array(
                    'type' => 'boolean',
                    'default' => true
                ),
                'showDescription' => array(
                    'type' => 'boolean',
                    'default' => true
                ),
                'useUrlParameter' => array(
                    'type' => 'boolean',
                    'default' => true
                )
            )
        ));
    }
    
    /**
     * Enqueue block editor assets
     */
    public function enqueue_block_editor_assets() {
        // Only enqueue in block editor
        if (!is_admin()) {
            return;
        }

        wp_enqueue_script(
            'glowess-city-info-block',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/js/city-info-block.js',
            array('wp-blocks', 'wp-element', 'wp-block-editor', 'wp-components'),
            GLOWESS_CITY_ECOMMERCE_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('glowess-city-info-block', 'cityInfoBlock', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('city_info_block_nonce')
        ));
    }
    
    /**
     * Get cities list for dropdown
     */
    public function get_cities_list() {
        check_ajax_referer('city_info_block_nonce', 'nonce');
        
        $cities = get_posts(array(
            'post_type' => 'city',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        $cities_list = array();
        foreach ($cities as $city) {
            $cities_list[] = array(
                'value' => $city->post_name,
                'label' => $city->post_title
            );
        }
        
        wp_send_json_success($cities_list);
    }
    
    /**
     * Render the block
     */
    public function render_block($attributes) {
        $city_slug = '';
        $show_title = $attributes['showTitle'] ?? true;
        $show_description = $attributes['showDescription'] ?? true;
        $use_url_parameter = $attributes['useUrlParameter'] ?? true;
        
        // Determine which city to show
        if ($use_url_parameter && isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);
        } elseif (!empty($attributes['citySlug'])) {
            $city_slug = sanitize_text_field($attributes['citySlug']);
        }
        
        if (empty($city_slug)) {
            return '<div class="city-info-notice">' . __('Şehir seçilmedi veya URL parametresi bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }
        
        // Get city data
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $city_slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));
        
        if (empty($cities)) {
            return '<div class="city-info-notice">' . __('Şehir bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }
        
        $city = $cities[0];
        
        // Get meta data
        $city_title = get_post_meta($city->ID, '_city_info_title', true);
        $city_description = get_post_meta($city->ID, '_city_info_description', true);
        
        // Start output
        ob_start();
        ?>
        <div class="city-info-container city-info-block">
            <?php if ($show_title && !empty($city_title)) : ?>
                <div class="city-info-title-section">
                    <h2 class="city-info-title"><?php echo esc_html($city_title); ?></h2>
                </div>
            <?php endif; ?>
            
            <?php if ($show_description && !empty($city_description)) : ?>
                <div class="city-info-description-section">
                    <div class="city-info-description">
                        <?php echo wp_kses_post($city_description); ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ((!$show_title || empty($city_title)) && (!$show_description || empty($city_description))) : ?>
                <div class="city-info-notice">
                    <?php echo sprintf(__('%s için bilgi bulunamadı.', 'glowess-city-ecommerce'), esc_html($city->post_title)); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        
        return ob_get_clean();
    }
}
