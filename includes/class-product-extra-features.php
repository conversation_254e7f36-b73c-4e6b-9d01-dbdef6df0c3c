<?php
/**
 * Product Extra Features
 * Ürünler için ek <PERSON> (hover efektleri, animasyonlar vs.)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_Product_Extra_Features {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Hover efekti için CSS ve JS ekle
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Ürün görsellerine hover efekti ekle
        add_action('wp_footer', array($this, 'add_product_hover_effect'));

        // Ana sayfa ürün bloklarına rating ekle
        add_action('wp_footer', array($this, 'add_rating_to_homepage_products'));

        // Ürün sayfasına şehir tab'ı ekle
        add_filter('woocommerce_product_tabs', array($this, 'add_city_delivery_tab'));

        // Tab görünürlüğünü düzelt
        add_action('wp_footer', array($this, 'fix_city_tab_visibility'));

        // Mo<PERSON> sticky sepete ekle butonu
        add_action('wp_footer', array($this, 'add_mobile_sticky_add_to_cart'));

        // Ürün değerlendirmelerini özelleştir
        add_action('init', array($this, 'customize_product_ratings'));

        // AJAX handler'ları
        add_action('wp_ajax_get_product_gallery', array($this, 'get_product_gallery_ajax'));
        add_action('wp_ajax_nopriv_get_product_gallery', array($this, 'get_product_gallery_ajax'));
        add_action('wp_ajax_get_product_rating', array($this, 'get_product_rating_ajax'));
        add_action('wp_ajax_nopriv_get_product_rating', array($this, 'get_product_rating_ajax'));
    }
    
    /**
     * CSS ve JS dosyalarını yükle
     */
    public function enqueue_scripts() {
        // Sadece shop, kategori ve ana sayfada yükle
        if (is_shop() || is_product_category() || is_product_tag() || is_home() || is_front_page()) {
            // CSS inline olarak ekleyeceğiz
            wp_add_inline_style('woocommerce-general', $this->get_hover_css());
        }
    }
    
    /**
     * Hover efekti CSS'i
     */
    private function get_hover_css() {
        return '
        .product-hover-container {
            position: relative;
            overflow: hidden;
        }
        
        .product-hover-container .product-image-primary {
            transition: opacity 0.3s ease;
        }
        
        .product-hover-container .product-image-secondary {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2;
        }
        
        .product-hover-container:hover .product-image-primary {
            opacity: 0;
        }
        
        .product-hover-container:hover .product-image-secondary {
            opacity: 1;
        }
        
        .product-hover-container .product-image-secondary img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Ürün Değerlendirme Stilleri */
        .glowess-product-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            margin: 8px 0;
        }

        .glowess-product-rating .rating-star {
            font-size: 16px;
        }

        .glowess-product-rating .rating-score {
            font-weight: 600;
            color: #333;
        }

        .glowess-product-rating .rating-separator {
            color: #999;
        }

        .glowess-product-rating .rating-count {
            color: #666;
            font-size: 13px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .glowess-product-rating {
                font-size: 13px;
            }

            .glowess-product-rating .rating-star {
                font-size: 15px;
            }
        }

        /* Şehir Teslimat Tab Stilleri */
        .city-delivery-content {
            padding: 20px 0;
            line-height: 1.6;
        }

        .city-delivery-content h1,
        .city-delivery-content h2,
        .city-delivery-content h3 {
            margin-top: 20px;
            margin-bottom: 15px;
            color: #333;
        }

        .city-delivery-content p {
            margin-bottom: 15px;
        }

        .city-delivery-content ul,
        .city-delivery-content ol {
            margin-bottom: 15px;
            padding-left: 20px;
        }

        .city-delivery-content li {
            margin-bottom: 8px;
        }

        /* Şehir Tab Başlığı Düzeltmeleri */
        .wc-tabs li.city_delivery_tab,
        .woocommerce-tabs ul.tabs li.city_delivery_tab {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            width: auto !important;
            height: auto !important;
        }

        .wc-tabs li.city_delivery_tab a,
        .woocommerce-tabs ul.tabs li.city_delivery_tab a {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: inherit !important;
            text-decoration: none !important;
            padding: 10px 15px !important;
            border: 1px solid #ddd !important;
            background: #f8f8f8 !important;
            margin-right: 5px !important;
        }

        .wc-tabs li.city_delivery_tab.active a,
        .woocommerce-tabs ul.tabs li.city_delivery_tab.active a {
            background: #fff !important;
            border-bottom-color: #fff !important;
            color: #333 !important;
        }

        /* Tema override */
        #tab-title-city_delivery {
            display: block !important;
        }

        #tab-title-city_delivery a {
            display: block !important;
            visibility: visible !important;
        }

        /* Mobil Sticky Sepete Ekle Form */
        .mobile-sticky-add-to-cart-form {
            display: none !important;
            position: fixed !important;
            bottom: 63px !important;
            left: 0 !important;
            right: 0 !important;
            width: 100vw !important;
            max-width: 100% !important;
            background: #fff !important;
            border-top: 1px solid #ddd !important;
            padding: 15px 20px !important;
            z-index: 999999 !important;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;
            margin: 0 !important;
            transform: none !important;
            visibility: visible !important;
            opacity: 1 !important;
            box-sizing: border-box !important;
        }

        .mobile-sticky-add-to-cart-form .cart {
            display: flex !important;
            align-items: center !important;
            gap: 15px !important;
            margin: 0 !important;
        }

        .mobile-sticky-add-to-cart-form .quantity {
            flex: 0 0 auto !important;
        }

        .mobile-sticky-add-to-cart-form .single_add_to_cart_button {
            flex: 1 !important;
            margin: 0 !important;
            padding: 12px 20px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            background: #000 !important;
            color: #fff !important;
            border: none !important;
            border-radius: 5px !important;
            transition: all 0.3s ease !important;
        }

        .mobile-sticky-add-to-cart-form .single_add_to_cart_button:hover,
        .mobile-sticky-add-to-cart-form .single_add_to_cart_button:active,
        .mobile-sticky-add-to-cart-form .single_add_to_cart_button:focus {
            background: #fff !important;
            color: #000 !important;
            border: 1px solid #000 !important;
        }

        /* Sadece mobilde göster */
        @media (max-width: 768px) {
            .mobile-sticky-add-to-cart-form {
                display: block !important;
            }

            /* Sayfanın altına boşluk ekle */
            body.single-product {
                padding-bottom: 100px !important;
            }
        }

        /* Ekstra güçlü CSS - tema override */
        @media screen and (max-width: 768px) {
            body .mobile-sticky-add-to-cart-form,
            html .mobile-sticky-add-to-cart-form,
            .mobile-sticky-add-to-cart-form {
                display: block !important;
                position: fixed !important;
                bottom: 63px !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 999999 !important;
                background: white !important;
            }
        }
        ';
    }
    
    /**
     * Ürün hover efektini ekle
     */
    public function add_product_hover_effect() {
        // Sadece shop, kategori sayfalarında çalış
        if (!is_shop() && !is_product_category() && !is_product_tag() && !is_home() && !is_front_page()) {
            return;
        }
        
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tüm ürün görsellerini bul
            var productImages = document.querySelectorAll('.woocommerce ul.products li.product .woocommerce-loop-product__link img, .wc-block-grid__product-image img, .product-item img');
            
            productImages.forEach(function(img) {
                // Ürün ID'sini al
                var productLink = img.closest('a');
                if (!productLink) return;
                
                var href = productLink.getAttribute('href');
                if (!href) return;
                
                // Ürün ID'sini URL'den çıkar
                var productId = extractProductIdFromUrl(href);
                if (!productId) return;
                
                // AJAX ile ürünün galeri görsellerini al
                fetchProductGallery(productId, img);
            });
        });
        
        function extractProductIdFromUrl(url) {
            // URL'den ürün ID'sini çıkar
            var matches = url.match(/\/urun\/([^\/]+)\//);
            if (matches && matches[1]) {
                return matches[1]; // Ürün slug'ı
            }
            return null;
        }
        
        function fetchProductGallery(productSlug, imgElement) {
            // WordPress AJAX ile ürün galeri görsellerini al
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo admin_url('admin-ajax.php'); ?>', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success && response.data.gallery && response.data.gallery.length > 0) {
                            setupHoverEffect(imgElement, response.data.gallery[0]);
                        }
                    } catch (e) {
                        console.log('Gallery fetch error:', e);
                    }
                }
            };
            
            xhr.send('action=get_product_gallery&product_slug=' + productSlug);
        }
        
        function setupHoverEffect(primaryImg, secondaryImgUrl) {
            var container = primaryImg.parentElement;
            
            // Container'ı hover container yap
            container.classList.add('product-hover-container');
            
            // Primary image'ı işaretle
            primaryImg.classList.add('product-image-primary');
            
            // Secondary image oluştur
            var secondaryDiv = document.createElement('div');
            secondaryDiv.classList.add('product-image-secondary');
            
            var secondaryImg = document.createElement('img');
            secondaryImg.src = secondaryImgUrl;
            secondaryImg.alt = primaryImg.alt;
            
            secondaryDiv.appendChild(secondaryImg);
            container.appendChild(secondaryDiv);
        }
        </script>
        <?php
    }
    
    /**
     * AJAX handler - Ürün galeri görsellerini getir
     */
    public function get_product_gallery_ajax() {
        $product_slug = sanitize_text_field($_POST['product_slug']);
        
        if (empty($product_slug)) {
            wp_die();
        }
        
        // Ürünü slug ile bul
        $product = get_page_by_path($product_slug, OBJECT, 'product');
        
        if (!$product) {
            wp_send_json_error('Product not found');
        }
        
        $product_obj = wc_get_product($product->ID);
        
        if (!$product_obj) {
            wp_send_json_error('Product object not found');
        }
        
        // Galeri görsellerini al
        $gallery_ids = $product_obj->get_gallery_image_ids();
        $gallery_urls = array();
        
        foreach ($gallery_ids as $gallery_id) {
            $gallery_urls[] = wp_get_attachment_image_url($gallery_id, 'woocommerce_thumbnail');
        }
        
        wp_send_json_success(array(
            'gallery' => $gallery_urls
        ));
    }

    /**
     * Ürün değerlendirmelerini özelleştir
     */
    public function customize_product_ratings() {
        // Tema'nın mevcut rating'lerini kaldır
        remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5);
        remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_rating', 10);

        // Tema'nın diğer olası rating hook'larını da kaldır
        add_action('init', array($this, 'remove_theme_ratings'), 15);

        // Bizim özel rating'imizi ekle - ürün başlığından hemen sonra
        add_action('woocommerce_after_shop_loop_item_title', array($this, 'display_custom_rating'), 6);
        add_action('woocommerce_single_product_summary', array($this, 'display_custom_rating'), 6);

        // Ana sayfa ve diğer sayfalardaki ürün bloklarına da ekle
        add_action('woocommerce_shop_loop_item_title', array($this, 'display_custom_rating_after_title'), 15);

        // WooCommerce block'ları için
        add_filter('woocommerce_blocks_product_grid_item_html', array($this, 'add_rating_to_blocks'), 10, 3);

        // WooCommerce rating HTML'ini özelleştir (fallback için)
        add_filter('woocommerce_product_get_rating_html', array($this, 'custom_rating_html'), 10, 3);
    }

    /**
     * Özel rating'i göster
     */
    public function display_custom_rating() {
        global $product;

        if (!$product) {
            return;
        }

        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ($rating_count > 0) {
            echo '<div class="glowess-product-rating">';
            echo '<span class="rating-star">⭐</span>';
            echo '<span class="rating-score">' . number_format($average_rating, 1) . '</span>';
            echo '<span class="rating-separator"> / </span>';
            echo '<span class="rating-count">' . $rating_count . ' değerlendirme</span>';
            echo '</div>';
        }
    }

    /**
     * Ürün başlığından sonra rating göster (ana sayfa için)
     */
    public function display_custom_rating_after_title() {
        // Bu fonksiyon başlık hook'unda çalışıyor, rating'i başlıktan sonra göstermek için
        add_action('woocommerce_after_shop_loop_item_title', array($this, 'display_custom_rating'), 1);
    }

    /**
     * WooCommerce block'larına rating ekle
     */
    public function add_rating_to_blocks($html, $data, $product) {
        if (!$product) {
            return $html;
        }

        $rating_count = $product->get_rating_count();
        $average_rating = $product->get_average_rating();

        if ($rating_count > 0) {
            $rating_html = '<div class="glowess-product-rating">';
            $rating_html .= '<span class="rating-star">⭐</span>';
            $rating_html .= '<span class="rating-score">' . number_format($average_rating, 1) . '</span>';
            $rating_html .= '<span class="rating-separator"> / </span>';
            $rating_html .= '<span class="rating-count">' . $rating_count . ' değerlendirme</span>';
            $rating_html .= '</div>';

            // Rating'i ürün başlığından sonra ekle
            $html = str_replace('</h3>', '</h3>' . $rating_html, $html);
            // Alternatif olarak başlık div'inden sonra
            $html = str_replace('</div>', $rating_html . '</div>', $html);
        }

        return $html;
    }

    /**
     * Özel rating HTML'i (fallback)
     */
    public function custom_rating_html($rating_html, $rating, $count) {
        if ($rating > 0) {
            $rating_html = '<div class="glowess-product-rating">';
            $rating_html .= '<span class="rating-star">⭐</span>';
            $rating_html .= '<span class="rating-score">' . number_format($rating, 1) . '</span>';
            $rating_html .= '<span class="rating-separator"> / </span>';
            $rating_html .= '<span class="rating-count">' . $count . ' değerlendirme</span>';
            $rating_html .= '</div>';
        }

        return $rating_html;
    }

    /**
     * Tema'nın rating'lerini kaldır
     */
    public function remove_theme_ratings() {
        // Yaygın tema rating hook'larını kaldır
        remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 5);
        remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 10);
        remove_action('woocommerce_after_shop_loop_item_title', 'woocommerce_template_loop_rating', 15);

        // Tema'nın özel rating fonksiyonlarını kaldır (genel isimler)
        global $wp_filter;

        // woocommerce_after_shop_loop_item_title hook'undaki tüm rating fonksiyonlarını kontrol et
        if (isset($wp_filter['woocommerce_after_shop_loop_item_title'])) {
            foreach ($wp_filter['woocommerce_after_shop_loop_item_title']->callbacks as $priority => $callbacks) {
                foreach ($callbacks as $callback) {
                    if (is_array($callback['function']) && is_string($callback['function'][1])) {
                        $function_name = $callback['function'][1];
                        // Rating ile ilgili fonksiyonları kaldır
                        if (strpos($function_name, 'rating') !== false || strpos($function_name, 'star') !== false) {
                            remove_action('woocommerce_after_shop_loop_item_title', $callback['function'], $priority);
                        }
                    }
                }
            }
        }

        // CSS ile de gizle (fallback)
        add_action('wp_head', array($this, 'hide_theme_ratings_css'));
    }

    /**
     * Tema rating'lerini CSS ile gizle
     */
    public function hide_theme_ratings_css() {
        echo '<style>
        .star-rating:not(.glowess-product-rating .star-rating),
        .woocommerce-product-rating:not(.glowess-product-rating),
        .product-rating:not(.glowess-product-rating),
        .rating:not(.glowess-product-rating .rating) {
            display: none !important;
        }
        </style>';
    }

    /**
     * Ana sayfa ürün bloklarına JavaScript ile rating ekle
     */
    public function add_rating_to_homepage_products() {
        // Sadece ana sayfa ve ürün listesi sayfalarında çalış
        if (!is_home() && !is_front_page() && !is_shop() && !is_product_category()) {
            return;
        }

        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Ana sayfa ürün bloklarını bul
            var productItems = document.querySelectorAll('.product-item, .wc-block-grid__product, .product, li.product');

            productItems.forEach(function(item) {
                // Ürün başlığını bul
                var titleElement = item.querySelector('h2, h3, .product-title, .wc-block-grid__product-title');

                if (!titleElement) return;

                // Zaten rating varsa atla
                if (item.querySelector('.glowess-product-rating')) return;

                // Ürün linkinden ID'yi al
                var productLink = item.querySelector('a');
                if (!productLink) return;

                var href = productLink.getAttribute('href');
                if (!href) return;

                var productSlug = extractProductIdFromUrl(href);
                if (!productSlug) return;

                // AJAX ile ürün rating'ini al
                fetchProductRating(productSlug, titleElement);
            });
        });

        function fetchProductRating(productSlug, titleElement) {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '<?php echo admin_url('admin-ajax.php'); ?>', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success && response.data.rating_html) {
                            // Rating'i başlıktan sonra ekle
                            titleElement.insertAdjacentHTML('afterend', response.data.rating_html);
                        }
                    } catch (e) {
                        console.log('Rating fetch error:', e);
                    }
                }
            };

            xhr.send('action=get_product_rating&product_slug=' + productSlug);
        }
        </script>
        <?php
    }

    /**
     * AJAX handler - Ürün rating'ini getir
     */
    public function get_product_rating_ajax() {
        $product_slug = sanitize_text_field($_POST['product_slug']);

        if (empty($product_slug)) {
            wp_die();
        }

        // Ürünü slug ile bul
        $product = get_page_by_path($product_slug, OBJECT, 'product');

        if (!$product) {
            wp_send_json_error('Product not found');
        }

        $product_obj = wc_get_product($product->ID);

        if (!$product_obj) {
            wp_send_json_error('Product object not found');
        }

        $rating_count = $product_obj->get_rating_count();
        $average_rating = $product_obj->get_average_rating();

        $rating_html = '';

        if ($rating_count > 0) {
            $rating_html = '<div class="glowess-product-rating">';
            $rating_html .= '<span class="rating-star">⭐</span>';
            $rating_html .= '<span class="rating-score">' . number_format($average_rating, 1) . '</span>';
            $rating_html .= '<span class="rating-separator"> / </span>';
            $rating_html .= '<span class="rating-count">' . $rating_count . ' değerlendirme</span>';
            $rating_html .= '</div>';
        }

        wp_send_json_success(array(
            'rating_html' => $rating_html,
            'rating' => $average_rating,
            'count' => $rating_count
        ));
    }

    /**
     * Ürün sayfasına şehir teslimat tab'ı ekle
     */
    public function add_city_delivery_tab($tabs) {
        // City parametresini kontrol et
        $city_slug = $this->get_current_city_slug();

        if (!$city_slug) {
            return $tabs;
        }

        // Şehir bilgilerini al
        $city = $this->get_city_by_slug($city_slug);

        if (!$city) {
            return $tabs;
        }

        // Şehir içeriği var mı kontrol et
        $city_content = get_post_field('post_content', $city->ID);

        if (empty($city_content)) {
            return $tabs;
        }

        // Debug (admin için)
        if (current_user_can('manage_options') && isset($_GET['debug'])) {
            error_log("City Tab Debug - City: " . $city->post_title);
            error_log("City Tab Debug - Content Length: " . strlen($city_content));
            error_log("City Tab Debug - Existing Tabs: " . print_r(array_keys($tabs), true));
        }

        // Yeni tab ekle - description tab'ından hemen sonra
        $new_tabs = array();

        foreach ($tabs as $key => $tab) {
            $new_tabs[$key] = $tab;

            // Description tab'ından sonra city tab'ını ekle
            if ($key === 'description') {
                $new_tabs['city_delivery'] = array(
                    'title'    => $city->post_title . ' Teslimat Detayları',
                    'priority' => 15,
                    'callback' => array($this, 'city_delivery_tab_content')
                );
            }
        }

        // Eğer description tab yoksa sona ekle
        if (!isset($new_tabs['city_delivery'])) {
            $new_tabs['city_delivery'] = array(
                'title'    => $city->post_title . ' Teslimat Detayları',
                'priority' => 15,
                'callback' => array($this, 'city_delivery_tab_content')
            );
        }

        $tabs = $new_tabs;

        // Debug
        if (current_user_can('manage_options') && isset($_GET['debug'])) {
            error_log("City Tab Debug - Tab Added: " . $city->post_title . ' Teslimat Detayları');
        }

        return $tabs;
    }

    /**
     * Şehir teslimat tab içeriği
     */
    public function city_delivery_tab_content() {
        $city_slug = $this->get_current_city_slug();

        if (!$city_slug) {
            return;
        }

        $city = $this->get_city_by_slug($city_slug);

        if (!$city) {
            return;
        }

        // Şehir içeriğini al ve göster
        $city_content = get_post_field('post_content', $city->ID);

        if (!empty($city_content)) {
            echo '<div class="city-delivery-content">';
            echo apply_filters('the_content', $city_content);
            echo '</div>';
        }
    }

    /**
     * City slug'ını al
     */
    private function get_current_city_slug() {
        if (isset($_GET['city']) && !empty($_GET['city'])) {
            return sanitize_text_field($_GET['city']);
        }
        return false;
    }

    /**
     * City'yi slug ile bul
     */
    private function get_city_by_slug($slug) {
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));

        return !empty($cities) ? $cities[0] : false;
    }

    /**
     * Şehir tab görünürlüğünü JavaScript ile düzelt
     */
    public function fix_city_tab_visibility() {
        // Sadece ürün sayfalarında çalış
        if (!is_product()) {
            return;
        }

        // City parametresi var mı kontrol et
        $city_slug = $this->get_current_city_slug();
        if (!$city_slug) {
            return;
        }

        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // City tab'ını bul ve görünür yap
            var cityTab = document.querySelector('#tab-title-city_delivery, .city_delivery_tab');

            if (cityTab) {
                // Tab'ı görünür yap
                cityTab.style.display = 'block';
                cityTab.style.visibility = 'visible';
                cityTab.style.opacity = '1';

                // Tab linkini de görünür yap
                var cityTabLink = cityTab.querySelector('a');
                if (cityTabLink) {
                    cityTabLink.style.display = 'block';
                    cityTabLink.style.visibility = 'visible';
                    cityTabLink.style.opacity = '1';

                    // Eğer tab başlığı boşsa, doğru başlığı ekle
                    if (!cityTabLink.textContent.trim()) {
                        cityTabLink.textContent = '<?php echo esc_js($this->get_city_by_slug($city_slug)->post_title ?? "Şehir"); ?> Teslimat Detayları';
                    }
                }

                console.log('City tab made visible:', cityTab);
            } else {
                console.log('City tab not found');
            }
        });
        </script>
        <?php
    }

    /**
     * Mobil sticky sepete ekle butonu - Sadece mevcut butonu sticky yap
     */
    public function add_mobile_sticky_add_to_cart() {
        // Sadece ürün sayfalarında çalış
        if (!is_product()) {
            return;
        }

        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (window.innerWidth <= 768) {
                // Mevcut sepete ekle form'unu bul
                var addToCartForm = document.querySelector('.wc-block-add-to-cart-form, .wp-block-add-to-cart-form');

                if (addToCartForm) {
                    // Form'u klonla
                    var stickyForm = addToCartForm.cloneNode(true);
                    stickyForm.classList.add('mobile-sticky-add-to-cart-form');

                    // Sticky positioning force et
                    stickyForm.style.position = 'fixed';
                    stickyForm.style.bottom = '63px';
                    stickyForm.style.left = '0';
                    stickyForm.style.right = '0';
                    stickyForm.style.width = '100%';
                    stickyForm.style.zIndex = '999999';
                    stickyForm.style.background = 'white';
                    stickyForm.style.borderTop = '1px solid #ddd';
                    stickyForm.style.padding = '15px 20px';
                    stickyForm.style.boxShadow = '0 -2px 10px rgba(0,0,0,0.1)';
                    stickyForm.style.display = 'none'; // Başlangıçta gizli

                    // Body'ye ekle
                    document.body.appendChild(stickyForm);

                    console.log('Sticky form added and positioned');
                } else {
                    console.log('Add to cart form not found');
                }
            }
        });

        // Scroll ile sticky form görünürlüğünü kontrol et
        window.addEventListener('scroll', function() {
            if (window.innerWidth <= 768) {
                var stickyForm = document.querySelector('.mobile-sticky-add-to-cart-form');
                var mainForm = document.querySelector('.wc-block-add-to-cart-form:not(.mobile-sticky-add-to-cart-form), .wp-block-add-to-cart-form:not(.mobile-sticky-add-to-cart-form)');

                if (stickyForm && mainForm) {
                    var mainFormRect = mainForm.getBoundingClientRect();
                    var windowHeight = window.innerHeight;

                    // Ana form viewport'tan çıktıysa sticky form göster
                    if (mainFormRect.bottom < 0 || mainFormRect.top > windowHeight) {
                        stickyForm.style.display = 'block';
                        // Position'ı tekrar force et
                        stickyForm.style.position = 'fixed';
                        stickyForm.style.bottom = '63px';
                        stickyForm.style.left = '0';
                        stickyForm.style.right = '0';
                        stickyForm.style.zIndex = '999999';
                    } else {
                        stickyForm.style.display = 'none';
                    }
                }
            }
        });

        // Resize event'inde de kontrol et
        window.addEventListener('resize', function() {
            var stickyForm = document.querySelector('.mobile-sticky-add-to-cart-form');
            if (stickyForm) {
                if (window.innerWidth > 768) {
                    stickyForm.style.display = 'none';
                }
            }
        });
        </script>
        <?php
    }
}
