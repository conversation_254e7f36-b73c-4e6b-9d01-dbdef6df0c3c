<?php
/**
 * City Maps Class
 * 
 * Handles Google Maps integration for cities with location pins
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Maps {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add meta boxes for city locations
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_location_meta_boxes'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Add admin scripts for meta box
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Add settings to admin
        add_action('admin_init', array($this, 'register_maps_settings'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'city-locations',
            __('Şehir Konumları', 'glowess-city-ecommerce'),
            array($this, 'locations_meta_box'),
            'city',
            'normal',
            'high'
        );
    }
    
    /**
     * Locations meta box
     */
    public function locations_meta_box($post) {
        wp_nonce_field('city_locations_nonce', 'city_locations_nonce');
        
        $locations = get_post_meta($post->ID, '_city_locations', true);
        if (!is_array($locations)) {
            $locations = array();
        }
        
        // Ensure we have at least one empty location for adding
        if (empty($locations)) {
            $locations = array(array('title' => '', 'lat' => '', 'lng' => '', 'contact' => ''));
        }
        
        ?>
        <div id="city-locations-container">
            <p><?php _e('Şehir için haritada gösterilecek konumları ekleyebilirsiniz.', 'glowess-city-ecommerce'); ?></p>
            
            <div id="city-locations-list">
                <?php foreach ($locations as $index => $location) : ?>
                    <div class="city-location-item" data-index="<?php echo $index; ?>">
                        <h4><?php echo sprintf(__('Konum %d', 'glowess-city-ecommerce'), $index + 1); ?></h4>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Başlık', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[<?php echo $index; ?>][title]" 
                                           value="<?php echo esc_attr($location['title'] ?? ''); ?>" 
                                           class="regular-text" 
                                           placeholder="<?php _e('Konum başlığı', 'glowess-city-ecommerce'); ?>" />
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Enlem (Latitude)', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[<?php echo $index; ?>][lat]" 
                                           value="<?php echo esc_attr($location['lat'] ?? ''); ?>" 
                                           class="regular-text location-lat" 
                                           placeholder="41.0082" />
                                    <p class="description"><?php _e('Örnek: 41.0082', 'glowess-city-ecommerce'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Boylam (Longitude)', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[<?php echo $index; ?>][lng]" 
                                           value="<?php echo esc_attr($location['lng'] ?? ''); ?>" 
                                           class="regular-text location-lng" 
                                           placeholder="28.9784" />
                                    <p class="description"><?php _e('Örnek: 28.9784', 'glowess-city-ecommerce'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('İletişim Bilgisi', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <textarea name="city_locations[<?php echo $index; ?>][contact]" 
                                              rows="3" 
                                              class="large-text"
                                              placeholder="<?php _e('Adres, telefon, email vb.', 'glowess-city-ecommerce'); ?>"><?php echo esc_textarea($location['contact'] ?? ''); ?></textarea>
                                </td>
                            </tr>
                        </table>
                        
                        <div class="location-actions">
                            <button type="button" class="button remove-location" style="<?php echo $index === 0 && count($locations) === 1 ? 'display:none;' : ''; ?>">
                                <?php _e('Konumu Kaldır', 'glowess-city-ecommerce'); ?>
                            </button>
                        </div>
                        
                        <hr style="margin: 20px 0;">
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="location-controls">
                <button type="button" id="add-location" class="button button-secondary">
                    <?php _e('Yeni Konum Ekle', 'glowess-city-ecommerce'); ?>
                </button>
                
                <button type="button" id="get-current-location" class="button">
                    <?php _e('Mevcut Konumumu Al', 'glowess-city-ecommerce'); ?>
                </button>
            </div>
            
            <div id="location-preview-map" style="height: 300px; margin-top: 20px; border: 1px solid #ddd;"></div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var locationIndex = <?php echo count($locations); ?>;
            
            // Add new location
            $('#add-location').on('click', function() {
                var newLocation = `
                    <div class="city-location-item" data-index="${locationIndex}">
                        <h4><?php echo sprintf(__('Konum %s', 'glowess-city-ecommerce'), '${locationIndex + 1}'); ?></h4>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Başlık', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[${locationIndex}][title]" 
                                           value="" 
                                           class="regular-text" 
                                           placeholder="<?php _e('Konum başlığı', 'glowess-city-ecommerce'); ?>" />
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Enlem (Latitude)', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[${locationIndex}][lat]" 
                                           value="" 
                                           class="regular-text location-lat" 
                                           placeholder="41.0082" />
                                    <p class="description"><?php _e('Örnek: 41.0082', 'glowess-city-ecommerce'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('Boylam (Longitude)', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <input type="text" name="city_locations[${locationIndex}][lng]" 
                                           value="" 
                                           class="regular-text location-lng" 
                                           placeholder="28.9784" />
                                    <p class="description"><?php _e('Örnek: 28.9784', 'glowess-city-ecommerce'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label><?php _e('İletişim Bilgisi', 'glowess-city-ecommerce'); ?></label>
                                </th>
                                <td>
                                    <textarea name="city_locations[${locationIndex}][contact]" 
                                              rows="3" 
                                              class="large-text"
                                              placeholder="<?php _e('Adres, telefon, email vb.', 'glowess-city-ecommerce'); ?>"></textarea>
                                </td>
                            </tr>
                        </table>
                        
                        <div class="location-actions">
                            <button type="button" class="button remove-location">
                                <?php _e('Konumu Kaldır', 'glowess-city-ecommerce'); ?>
                            </button>
                        </div>
                        
                        <hr style="margin: 20px 0;">
                    </div>
                `;
                
                $('#city-locations-list').append(newLocation);
                locationIndex++;
                
                // Show remove buttons
                $('.remove-location').show();
            });
            
            // Remove location
            $(document).on('click', '.remove-location', function() {
                $(this).closest('.city-location-item').remove();
                
                // Hide remove button if only one location left
                if ($('.city-location-item').length === 1) {
                    $('.remove-location').hide();
                }
                
                // Re-index locations
                $('.city-location-item').each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('h4').text('<?php _e('Konum', 'glowess-city-ecommerce'); ?> ' + (index + 1));
                    
                    // Update input names
                    $(this).find('input, textarea').each(function() {
                        var name = $(this).attr('name');
                        if (name) {
                            name = name.replace(/\[\d+\]/, '[' + index + ']');
                            $(this).attr('name', name);
                        }
                    });
                });
            });
            
            // Get current location
            $('#get-current-location').on('click', function() {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(function(position) {
                        var lat = position.coords.latitude;
                        var lng = position.coords.longitude;
                        
                        // Fill the last location inputs
                        var lastLocation = $('.city-location-item').last();
                        lastLocation.find('.location-lat').val(lat);
                        lastLocation.find('.location-lng').val(lng);
                        
                        alert('<?php _e('Konum bilgisi alındı!', 'glowess-city-ecommerce'); ?>');
                    }, function() {
                        alert('<?php _e('Konum bilgisi alınamadı.', 'glowess-city-ecommerce'); ?>');
                    });
                } else {
                    alert('<?php _e('Tarayıcınız konum bilgisini desteklemiyor.', 'glowess-city-ecommerce'); ?>');
                }
            });
        });
        </script>
        
        <style>
        .city-location-item {
            background: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        
        .city-location-item h4 {
            margin-top: 0;
            color: #0073aa;
        }
        
        .location-actions {
            margin-top: 10px;
        }
        
        .location-controls {
            margin: 20px 0;
        }
        
        .location-controls .button {
            margin-right: 10px;
        }
        
        #location-preview-map {
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
        </style>
        <?php
    }

    /**
     * Save location meta boxes
     */
    public function save_location_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['city_locations_nonce']) || !wp_verify_nonce($_POST['city_locations_nonce'], 'city_locations_nonce')) {
            return;
        }

        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'city') {
            return;
        }

        // Save locations
        if (isset($_POST['city_locations'])) {
            $locations = array();
            foreach ($_POST['city_locations'] as $location_data) {
                // Only save if at least lat/lng are provided
                if (!empty($location_data['lat']) && !empty($location_data['lng'])) {
                    $locations[] = array(
                        'title' => sanitize_text_field($location_data['title']),
                        'lat' => floatval($location_data['lat']),
                        'lng' => floatval($location_data['lng']),
                        'contact' => sanitize_textarea_field($location_data['contact'])
                    );
                }
            }
            update_post_meta($post_id, '_city_locations', $locations);
        } else {
            delete_post_meta($post_id, '_city_locations');
        }
    }

    /**
     * Enqueue frontend scripts
     */
    public function enqueue_scripts() {
        // Only enqueue on pages that might have the shortcode
        if (is_singular() || is_home() || is_front_page()) {
            // Google Maps API
            $api_key = get_option('glowess_city_maps_api_key', '');
            if (!empty($api_key)) {
                wp_enqueue_script(
                    'google-maps-api',
                    'https://maps.googleapis.com/maps/api/js?key=' . esc_attr($api_key) . '&libraries=places',
                    array(),
                    null,
                    true
                );
            }

            // Custom maps script
            wp_enqueue_script(
                'glowess-city-maps',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/js/city-maps.js',
                array('jquery'),
                GLOWESS_CITY_ECOMMERCE_VERSION,
                true
            );

            // Maps CSS
            wp_enqueue_style(
                'glowess-city-maps',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/city-maps.css',
                array(),
                GLOWESS_CITY_ECOMMERCE_VERSION
            );
        }
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        global $post_type;

        if ($post_type === 'city' && ($hook === 'post.php' || $hook === 'post-new.php')) {
            // Google Maps API for admin
            $api_key = get_option('glowess_city_maps_api_key', '');
            if (!empty($api_key)) {
                wp_enqueue_script(
                    'google-maps-api-admin',
                    'https://maps.googleapis.com/maps/api/js?key=' . esc_attr($api_key) . '&libraries=places',
                    array(),
                    null,
                    true
                );
            }
        }
    }

    /**
     * Register maps settings
     */
    public function register_maps_settings() {
        register_setting('glowess_city_ecommerce_settings', 'glowess_city_maps_api_key');
        register_setting('glowess_city_ecommerce_settings', 'glowess_city_maps_default_zoom');
        register_setting('glowess_city_ecommerce_settings', 'glowess_city_maps_height');

        // Add settings fields to existing settings page
        add_settings_section(
            'maps_settings',
            __('Google Maps Ayarları', 'glowess-city-ecommerce'),
            array($this, 'maps_settings_callback'),
            'glowess-city-ecommerce-settings'
        );

        add_settings_field(
            'maps_api_key',
            __('Google Maps API Key', 'glowess-city-ecommerce'),
            array($this, 'maps_api_key_callback'),
            'glowess-city-ecommerce-settings',
            'maps_settings'
        );

        add_settings_field(
            'maps_default_zoom',
            __('Varsayılan Zoom Seviyesi', 'glowess-city-ecommerce'),
            array($this, 'maps_default_zoom_callback'),
            'glowess-city-ecommerce-settings',
            'maps_settings'
        );

        add_settings_field(
            'maps_height',
            __('Harita Yüksekliği', 'glowess-city-ecommerce'),
            array($this, 'maps_height_callback'),
            'glowess-city-ecommerce-settings',
            'maps_settings'
        );
    }

    /**
     * Maps settings section callback
     */
    public function maps_settings_callback() {
        echo '<p>' . __('Google Maps entegrasyonu için gerekli ayarlar.', 'glowess-city-ecommerce') . '</p>';
    }

    /**
     * Maps API key callback
     */
    public function maps_api_key_callback() {
        $api_key = get_option('glowess_city_maps_api_key', '');
        ?>
        <input type="text" name="glowess_city_maps_api_key" value="<?php echo esc_attr($api_key); ?>" class="regular-text" />
        <p class="description">
            <?php _e('Google Maps API anahtarınızı girin.', 'glowess-city-ecommerce'); ?>
            <a href="https://developers.google.com/maps/documentation/javascript/get-api-key" target="_blank">
                <?php _e('API anahtarı nasıl alınır?', 'glowess-city-ecommerce'); ?>
            </a>
        </p>
        <?php
    }

    /**
     * Maps default zoom callback
     */
    public function maps_default_zoom_callback() {
        $zoom = get_option('glowess_city_maps_default_zoom', '12');
        ?>
        <input type="number" name="glowess_city_maps_default_zoom" value="<?php echo esc_attr($zoom); ?>" min="1" max="20" />
        <p class="description"><?php _e('Haritanın varsayılan zoom seviyesi (1-20 arası).', 'glowess-city-ecommerce'); ?></p>
        <?php
    }

    /**
     * Maps height callback
     */
    public function maps_height_callback() {
        $height = get_option('glowess_city_maps_height', '400');
        ?>
        <input type="number" name="glowess_city_maps_height" value="<?php echo esc_attr($height); ?>" min="200" max="800" />
        <span>px</span>
        <p class="description"><?php _e('Haritanın yüksekliği piksel cinsinden.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }

    /**
     * Render maps shortcode
     */
    public function render_maps_shortcode($atts) {
        // Parse attributes
        $atts = shortcode_atts(array(
            'height' => get_option('glowess_city_maps_height', '400'),
            'zoom' => get_option('glowess_city_maps_default_zoom', '12'),
            'city' => '',
        ), $atts, 'city_maps');

        // Check if API key is set
        $api_key = get_option('glowess_city_maps_api_key', '');
        if (empty($api_key)) {
            if (current_user_can('manage_options')) {
                return '<div class="city-maps-error">' .
                       __('Google Maps API anahtarı ayarlanmamış.', 'glowess-city-ecommerce') .
                       ' <a href="' . admin_url('edit.php?post_type=city&page=glowess-city-ecommerce-settings') . '">' .
                       __('Ayarlar', 'glowess-city-ecommerce') . '</a></div>';
            }
            return '';
        }

        // Get city from parameter or shortcode attribute
        $city_slug = '';
        if (!empty($atts['city'])) {
            $city_slug = sanitize_text_field($atts['city']);
        } elseif (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);
        }

        if (empty($city_slug)) {
            return '<div class="city-maps-notice">' . __('Şehir parametresi bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        // Get city data
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $city_slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));

        if (empty($cities)) {
            return '<div class="city-maps-notice">' . __('Şehir bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        $city = $cities[0];
        $locations = get_post_meta($city->ID, '_city_locations', true);

        if (empty($locations) || !is_array($locations)) {
            return '<div class="city-maps-notice">' .
                   sprintf(__('%s için konum bilgisi bulunamadı.', 'glowess-city-ecommerce'), $city->post_title) .
                   '</div>';
        }

        // Generate unique map ID
        $map_id = 'city-map-' . uniqid();

        // Enqueue the script inline
        wp_add_inline_script('glowess-city-maps', $this->generate_map_script($map_id, $locations, $atts, $city));

        // Return only the HTML
        return '<div class="city-maps-container"><div id="' . esc_attr($map_id) . '" class="city-map" style="height: ' . intval($atts['height']) . 'px;"></div></div>';
    }

    /**
     * Generate map JavaScript
     */
    private function generate_map_script($map_id, $locations, $atts, $city) {
        $locations_json = wp_json_encode($locations);
        $js_function_name = 'initCityMap_' . str_replace('-', '_', $map_id);

        return "
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof google !== 'undefined' && google.maps) {
                {$js_function_name}();
            } else {
                console.error('Google Maps API not loaded');
            }
        });

        function {$js_function_name}() {
            var locations = {$locations_json};
            var mapElement = document.getElementById('{$map_id}');

            console.log('Map Debug - Locations:', locations);
            console.log('Map Debug - Map Element:', mapElement);

            if (!mapElement) {
                console.error('Map element not found: {$map_id}');
                return;
            }

            if (!locations || locations.length === 0) {
                console.error('No locations found');
                return;
            }

            // Calculate center point
            var bounds = new google.maps.LatLngBounds();
            var center = { lat: parseFloat(locations[0].lat), lng: parseFloat(locations[0].lng) };

            console.log('Map Debug - Center:', center);

            if (locations.length > 1) {
                locations.forEach(function(location) {
                    bounds.extend(new google.maps.LatLng(parseFloat(location.lat), parseFloat(location.lng)));
                });
                center = bounds.getCenter();
            }

            // Create map
            var map = new google.maps.Map(mapElement, {
                zoom: " . intval($atts['zoom']) . ",
                center: center,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });

            console.log('Map Debug - Map created:', map);

            // Add markers
            var infoWindow = new google.maps.InfoWindow();
            var markers = [];

            locations.forEach(function(location, index) {
                console.log('Map Debug - Creating marker for location:', location);

                var lat = parseFloat(location.lat);
                var lng = parseFloat(location.lng);

                if (isNaN(lat) || isNaN(lng)) {
                    console.error('Invalid coordinates for location:', location);
                    return;
                }

                var marker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: map,
                    title: location.title || '" . esc_js($city->post_title) . " - Konum ' + (index + 1),
                    animation: google.maps.Animation.DROP,
                    visible: true
                });

                markers.push(marker);
                console.log('Map Debug - Marker created:', marker);

                // Info window content
                var infoContent = '<div class=\"city-map-info\">';
                if (location.title) {
                    infoContent += '<h4>' + escapeHtml(location.title) + '</h4>';
                }
                if (location.contact) {
                    infoContent += '<p>' + escapeHtml(location.contact).replace(/\\n/g, '<br>') + '</p>';
                }
                infoContent += '<small>Lat: ' + location.lat + ', Lng: ' + location.lng + '</small>';
                infoContent += '</div>';

                marker.addListener('click', function() {
                    infoWindow.setContent(infoContent);
                    infoWindow.open(map, marker);
                });
            });

            console.log('Map Debug - Total markers created:', markers.length);

            // Fit bounds if multiple locations
            if (locations.length > 1) {
                map.fitBounds(bounds);

                // Ensure minimum zoom level
                google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                    if (map.getZoom() > " . intval($atts['zoom']) . ") {
                        map.setZoom(" . intval($atts['zoom']) . ");
                    }
                });
            }

            // Helper function to escape HTML
            function escapeHtml(text) {
                if (!text) return '';
                var map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '\"': '&quot;',
                    \"'\": '&#039;'
                };
                return String(text).replace(/[&<>\"']/g, function(m) { return map[m]; });
            }
        }";
    }
}
