<?php
/**
 * City Info Class
 * 
 * Handles city information meta fields and shortcode display
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_City_Info {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add meta boxes for city info
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_info_meta_boxes'));
        
        // Enqueue styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        add_meta_box(
            'city-info-details',
            __('Şehir Bilgileri', 'glowess-city-ecommerce'),
            array($this, 'info_details_meta_box'),
            'city',
            'normal',
            'high'
        );
    }
    
    /**
     * City info details meta box
     */
    public function info_details_meta_box($post) {
        wp_nonce_field('city_info_details_nonce', 'city_info_details_nonce');
        
        // Get existing values
        $population = get_post_meta($post->ID, '_city_population', true);
        $area = get_post_meta($post->ID, '_city_area', true);
        $founded = get_post_meta($post->ID, '_city_founded', true);
        $climate = get_post_meta($post->ID, '_city_climate', true);
        $timezone = get_post_meta($post->ID, '_city_timezone', true);
        $postal_code = get_post_meta($post->ID, '_city_postal_code', true);
        $phone_code = get_post_meta($post->ID, '_city_phone_code', true);
        $mayor = get_post_meta($post->ID, '_city_mayor', true);
        $website = get_post_meta($post->ID, '_city_website', true);
        $highlights = get_post_meta($post->ID, '_city_highlights', true);
        
        if (!is_array($highlights)) {
            $highlights = array();
        }
        
        // Ensure we have at least one empty highlight
        if (empty($highlights)) {
            $highlights = array('');
        }
        
        ?>
        <div id="city-info-container">
            <p><?php _e('Şehir hakkında genel bilgileri ekleyebilirsiniz.', 'glowess-city-ecommerce'); ?></p>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="city_population"><?php _e('Nüfus', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_population" name="city_population" 
                               value="<?php echo esc_attr($population); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: 2.500.000', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin toplam nüfusu', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_area"><?php _e('Yüzölçümü', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_area" name="city_area" 
                               value="<?php echo esc_attr($area); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: 1.417 km²', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin yüzölçümü', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_founded"><?php _e('Kuruluş Yılı', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_founded" name="city_founded" 
                               value="<?php echo esc_attr($founded); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: M.Ö. 158', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin kuruluş tarihi', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_climate"><?php _e('İklim', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_climate" name="city_climate" 
                               value="<?php echo esc_attr($climate); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: Akdeniz İklimi', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin iklim özellikleri', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_timezone"><?php _e('Saat Dilimi', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_timezone" name="city_timezone" 
                               value="<?php echo esc_attr($timezone); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: UTC+3', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin saat dilimi', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_postal_code"><?php _e('Posta Kodu', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_postal_code" name="city_postal_code" 
                               value="<?php echo esc_attr($postal_code); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: 07xxx', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin posta kodu', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_phone_code"><?php _e('Telefon Kodu', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_phone_code" name="city_phone_code" 
                               value="<?php echo esc_attr($phone_code); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Örn: 0242', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin telefon alan kodu', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_mayor"><?php _e('Belediye Başkanı', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="city_mayor" name="city_mayor" 
                               value="<?php echo esc_attr($mayor); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('Belediye başkanının adı', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Mevcut belediye başkanı', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="city_website"><?php _e('Resmi Website', 'glowess-city-ecommerce'); ?></label>
                    </th>
                    <td>
                        <input type="url" id="city_website" name="city_website" 
                               value="<?php echo esc_attr($website); ?>" 
                               class="regular-text" 
                               placeholder="<?php _e('https://www.sehir.bel.tr', 'glowess-city-ecommerce'); ?>" />
                        <p class="description"><?php _e('Şehrin resmi website adresi', 'glowess-city-ecommerce'); ?></p>
                    </td>
                </tr>
            </table>
            
            <h3><?php _e('Öne Çıkan Özellikler', 'glowess-city-ecommerce'); ?></h3>
            <p class="description"><?php _e('Şehrin öne çıkan özelliklerini, turistik yerlerini veya önemli noktalarını ekleyebilirsiniz.', 'glowess-city-ecommerce'); ?></p>
            
            <div id="city-highlights-list">
                <?php foreach ($highlights as $index => $highlight) : ?>
                    <div class="city-highlight-item" data-index="<?php echo $index; ?>">
                        <input type="text" name="city_highlights[<?php echo $index; ?>]" 
                               value="<?php echo esc_attr($highlight); ?>" 
                               class="large-text" 
                               placeholder="<?php _e('Örn: Kaleiçi Tarihi Bölgesi', 'glowess-city-ecommerce'); ?>" />
                        <button type="button" class="button remove-highlight" style="<?php echo $index === 0 && count($highlights) === 1 ? 'display:none;' : ''; ?>">
                            <?php _e('Kaldır', 'glowess-city-ecommerce'); ?>
                        </button>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="highlight-controls">
                <button type="button" id="add-highlight" class="button button-secondary">
                    <?php _e('Yeni Özellik Ekle', 'glowess-city-ecommerce'); ?>
                </button>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            var highlightIndex = <?php echo count($highlights); ?>;
            
            // Add new highlight
            $('#add-highlight').on('click', function() {
                var newHighlight = `
                    <div class="city-highlight-item" data-index="${highlightIndex}">
                        <input type="text" name="city_highlights[${highlightIndex}]" 
                               value="" 
                               class="large-text" 
                               placeholder="<?php _e('Örn: Kaleiçi Tarihi Bölgesi', 'glowess-city-ecommerce'); ?>" />
                        <button type="button" class="button remove-highlight">
                            <?php _e('Kaldır', 'glowess-city-ecommerce'); ?>
                        </button>
                    </div>
                `;
                
                $('#city-highlights-list').append(newHighlight);
                highlightIndex++;
                
                // Show remove buttons
                $('.remove-highlight').show();
            });
            
            // Remove highlight
            $(document).on('click', '.remove-highlight', function() {
                $(this).closest('.city-highlight-item').remove();
                
                // Hide remove button if only one highlight left
                if ($('.city-highlight-item').length === 1) {
                    $('.remove-highlight').hide();
                }
                
                // Re-index highlights
                $('.city-highlight-item').each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('input').attr('name', 'city_highlights[' + index + ']');
                });
            });
        });
        </script>
        
        <style>
        .city-highlight-item {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .city-highlight-item input {
            flex: 1;
        }
        
        .highlight-controls {
            margin-top: 15px;
        }
        
        #city-info-container h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #0073aa;
        }
        </style>
        <?php
    }

    /**
     * Save info meta boxes
     */
    public function save_info_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['city_info_details_nonce']) || !wp_verify_nonce($_POST['city_info_details_nonce'], 'city_info_details_nonce')) {
            return;
        }

        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'city') {
            return;
        }

        // Save basic info fields
        $fields = array(
            'city_population' => '_city_population',
            'city_area' => '_city_area',
            'city_founded' => '_city_founded',
            'city_climate' => '_city_climate',
            'city_timezone' => '_city_timezone',
            'city_postal_code' => '_city_postal_code',
            'city_phone_code' => '_city_phone_code',
            'city_mayor' => '_city_mayor',
            'city_website' => '_city_website'
        );

        foreach ($fields as $field => $meta_key) {
            if (isset($_POST[$field])) {
                $value = sanitize_text_field($_POST[$field]);
                if (!empty($value)) {
                    update_post_meta($post_id, $meta_key, $value);
                } else {
                    delete_post_meta($post_id, $meta_key);
                }
            }
        }

        // Save highlights
        if (isset($_POST['city_highlights'])) {
            $highlights = array();
            foreach ($_POST['city_highlights'] as $highlight) {
                $highlight = sanitize_text_field($highlight);
                if (!empty($highlight)) {
                    $highlights[] = $highlight;
                }
            }

            if (!empty($highlights)) {
                update_post_meta($post_id, '_city_highlights', $highlights);
            } else {
                delete_post_meta($post_id, '_city_highlights');
            }
        } else {
            delete_post_meta($post_id, '_city_highlights');
        }
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Only enqueue on pages that might have the shortcode
        if (is_singular() || is_home() || is_front_page()) {
            wp_enqueue_style(
                'glowess-city-info',
                GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/city-info.css',
                array(),
                GLOWESS_CITY_ECOMMERCE_VERSION
            );
        }
    }

    /**
     * Render info shortcode
     */
    public function render_info_shortcode($atts) {
        // Parse attributes
        $atts = shortcode_atts(array(
            'city' => '',
            'style' => 'default', // default, compact, detailed
            'show_highlights' => 'yes',
            'show_contact' => 'yes',
            'show_stats' => 'yes'
        ), $atts, 'city_info');

        // Get city from parameter or shortcode attribute
        $city_slug = '';
        if (!empty($atts['city'])) {
            $city_slug = sanitize_text_field($atts['city']);
        } elseif (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);
        }

        if (empty($city_slug)) {
            return '<div class="city-info-notice">' . __('Şehir parametresi bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        // Get city data
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $city_slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));

        if (empty($cities)) {
            return '<div class="city-info-notice">' . __('Şehir bulunamadı.', 'glowess-city-ecommerce') . '</div>';
        }

        $city = $cities[0];

        // Get meta data
        $population = get_post_meta($city->ID, '_city_population', true);
        $area = get_post_meta($city->ID, '_city_area', true);
        $founded = get_post_meta($city->ID, '_city_founded', true);
        $climate = get_post_meta($city->ID, '_city_climate', true);
        $timezone = get_post_meta($city->ID, '_city_timezone', true);
        $postal_code = get_post_meta($city->ID, '_city_postal_code', true);
        $phone_code = get_post_meta($city->ID, '_city_phone_code', true);
        $mayor = get_post_meta($city->ID, '_city_mayor', true);
        $website = get_post_meta($city->ID, '_city_website', true);
        $highlights = get_post_meta($city->ID, '_city_highlights', true);

        // Start output
        ob_start();
        ?>
        <div class="city-info-container city-info-style-<?php echo esc_attr($atts['style']); ?>">
            <div class="city-info-header">
                <h2 class="city-info-title"><?php echo esc_html($city->post_title); ?></h2>
                <?php if (!empty($city->post_content)) : ?>
                    <div class="city-info-description">
                        <?php echo wp_kses_post($city->post_content); ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($atts['show_stats'] === 'yes') : ?>
                <div class="city-info-stats">
                    <h3><?php _e('Genel Bilgiler', 'glowess-city-ecommerce'); ?></h3>
                    <div class="city-stats-grid">
                        <?php if (!empty($population)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('Nüfus:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($population); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($area)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('Yüzölçümü:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($area); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($founded)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('Kuruluş:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($founded); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($climate)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('İklim:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($climate); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($timezone)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('Saat Dilimi:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($timezone); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($mayor)) : ?>
                            <div class="city-stat-item">
                                <span class="stat-label"><?php _e('Belediye Başkanı:', 'glowess-city-ecommerce'); ?></span>
                                <span class="stat-value"><?php echo esc_html($mayor); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($atts['show_contact'] === 'yes') : ?>
                <div class="city-info-contact">
                    <h3><?php _e('İletişim Bilgileri', 'glowess-city-ecommerce'); ?></h3>
                    <div class="city-contact-grid">
                        <?php if (!empty($postal_code)) : ?>
                            <div class="city-contact-item">
                                <span class="contact-label"><?php _e('Posta Kodu:', 'glowess-city-ecommerce'); ?></span>
                                <span class="contact-value"><?php echo esc_html($postal_code); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($phone_code)) : ?>
                            <div class="city-contact-item">
                                <span class="contact-label"><?php _e('Telefon Kodu:', 'glowess-city-ecommerce'); ?></span>
                                <span class="contact-value"><?php echo esc_html($phone_code); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($website)) : ?>
                            <div class="city-contact-item">
                                <span class="contact-label"><?php _e('Resmi Website:', 'glowess-city-ecommerce'); ?></span>
                                <span class="contact-value">
                                    <a href="<?php echo esc_url($website); ?>" target="_blank" rel="noopener">
                                        <?php echo esc_html($website); ?>
                                    </a>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($atts['show_highlights'] === 'yes' && !empty($highlights) && is_array($highlights)) : ?>
                <div class="city-info-highlights">
                    <h3><?php _e('Öne Çıkan Özellikler', 'glowess-city-ecommerce'); ?></h3>
                    <ul class="city-highlights-list">
                        <?php foreach ($highlights as $highlight) : ?>
                            <li class="highlight-item"><?php echo esc_html($highlight); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        <?php

        return ob_get_clean();
    }
}
