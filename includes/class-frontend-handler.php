<?php
/**
 * Frontend Handler Class
 * 
 * Handles frontend functionality and user interface
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_Frontend_Handler {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Add city info to single product page
        
        // Modify page title when city is selected
        add_filter('wp_title', array($this, 'modify_page_title'), 10, 3);
        add_filter('document_title_parts', array($this, 'modify_document_title_parts'));

    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue CSS
        wp_enqueue_style(
            'glowess-city-ecommerce',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            GLOWESS_CITY_ECOMMERCE_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'glowess-city-ecommerce',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            GLOWESS_CITY_ECOMMERCE_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('glowess-city-ecommerce', 'glowess_city_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('glowess_city_nonce'),
            'strings' => array(
                'loading' => __('Yükleniyor...', 'glowess-city-ecommerce'),
                'no_products' => __('Bu şehir için ürün bulunamadı.', 'glowess-city-ecommerce'),
                'no_categories' => __('Bu şehir için kategori bulunamadı.', 'glowess-city-ecommerce'),
                'error' => __('Bir hata oluştu.', 'glowess-city-ecommerce')
            )
        ));
    }
    
    /**
     * Add city info to single product page
     */
    
    
    /**
     * Modify page title when city is selected
     */
    public function modify_page_title($title, $sep, $seplocation) {
        $city_slug = isset($_GET['city']) ? sanitize_text_field($_GET['city']) : '';
        
        if ($city_slug && (is_shop() || is_product_category() || is_product_tag())) {
            $city = get_posts(array(
                'post_type' => 'city',
                'name' => $city_slug,
                'post_status' => 'publish',
                'posts_per_page' => 1
            ));
            
            if (!empty($city)) {
                $city_name = $city[0]->post_title;
                
                if ($seplocation === 'right') {
                    $title = $city_name . ' ' . $sep . ' ' . $title;
                } else {
                    $title = $title . ' ' . $sep . ' ' . $city_name;
                }
            }
        }
        
        return $title;
    }
    
    /**
     * Modify document title parts
     */
    public function modify_document_title_parts($title_parts) {
        $city_slug = isset($_GET['city']) ? sanitize_text_field($_GET['city']) : '';
        
        if ($city_slug && (is_shop() || is_product_category() || is_product_tag())) {
            $city = get_posts(array(
                'post_type' => 'city',
                'name' => $city_slug,
                'post_status' => 'publish',
                'posts_per_page' => 1
            ));
            
            if (!empty($city)) {
                $title_parts['title'] = $city[0]->post_title . ' - ' . $title_parts['title'];
            }
        }
        
        return $title_parts;
    }
    
    /**
     * Get current city data
     */
    public function get_current_city() {
        $city_slug = isset($_GET['city']) ? sanitize_text_field($_GET['city']) : '';
        
        if (!$city_slug) {
            return false;
        }
        
        $cities = get_posts(array(
            'post_type' => 'city',
            'name' => $city_slug,
            'post_status' => 'publish',
            'posts_per_page' => 1
        ));
        
        return !empty($cities) ? $cities[0] : false;
    }
    
    /**
     * Display city banner/hero section
     */
    public function display_city_hero($city_id = null) {
        if (!$city_id) {
            $current_city = $this->get_current_city();
            if (!$current_city) {
                return;
            }
            $city_id = $current_city->ID;
        }
        
        $city = get_post($city_id);
        if (!$city) {
            return;
        }
        
        $images = get_post_meta($city_id, '_city_gallery_images', true);
        
        ?>
        <div class="city-hero-section">
            <?php if ($images && is_array($images) && !empty($images[0]['id'])) : ?>
                <div class="city-hero-image">
                    <?php
                    $image_url = wp_get_attachment_image_url($images[0]['id'], 'full');
                    if ($image_url) :
                    ?>
                        <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($city->post_title); ?>" />
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="city-hero-content">
                <h1><?php echo esc_html($city->post_title); ?></h1>
                <?php if (!empty($city->post_content)) : ?>
                    <div class="city-description">
                        <?php echo wp_kses_post($city->post_content); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
}

/**
 * City Widget Class
 */
class Glowess_City_Widget extends WP_Widget {
    
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct(
            'glowess_city_widget',
            __('Şehir Seçici', 'glowess-city-ecommerce'),
            array(
                'description' => __('Ziyaretçilerin şehir seçmesini sağlar.', 'glowess-city-ecommerce')
            )
        );
    }
    
    /**
     * Widget output
     */
    public function widget($args, $instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Şehir Seçin', 'glowess-city-ecommerce');
        $show_all_option = !empty($instance['show_all_option']) ? $instance['show_all_option'] : true;
        
        $cities = get_posts(array(
            'post_type' => 'city',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        if (empty($cities)) {
            return;
        }
        
        $current_city = isset($_GET['city']) ? sanitize_text_field($_GET['city']) : '';
        
        echo $args['before_widget'];
        
        if ($title) {
            echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
        }
        
        ?>
        <form method="get" class="city-widget-form">
            <select name="city" class="city-widget-selector" onchange="this.form.submit()">
                <?php if ($show_all_option) : ?>
                    <option value=""><?php _e('Tüm şehirler', 'glowess-city-ecommerce'); ?></option>
                <?php endif; ?>
                
                <?php foreach ($cities as $city) : ?>
                    <option value="<?php echo esc_attr($city->post_name); ?>" <?php selected($current_city, $city->post_name); ?>>
                        <?php echo esc_html($city->post_title); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            
            <?php
            // Preserve other query parameters
            foreach ($_GET as $key => $value) {
                if ($key !== 'city' && !empty($value)) {
                    echo '<input type="hidden" name="' . esc_attr($key) . '" value="' . esc_attr($value) . '" />';
                }
            }
            ?>
        </form>
        <?php
        
        echo $args['after_widget'];
    }
    
    /**
     * Widget form
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Şehir Seçin', 'glowess-city-ecommerce');
        $show_all_option = !empty($instance['show_all_option']) ? $instance['show_all_option'] : true;
        
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Başlık:', 'glowess-city-ecommerce'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_all_option); ?> id="<?php echo esc_attr($this->get_field_id('show_all_option')); ?>" name="<?php echo esc_attr($this->get_field_name('show_all_option')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_all_option')); ?>"><?php _e('"Tüm şehirler" seçeneğini göster', 'glowess-city-ecommerce'); ?></label>
        </p>
        <?php
    }
    
    /**
     * Update widget
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_all_option'] = (!empty($new_instance['show_all_option'])) ? 1 : 0;
        
        return $instance;
    }
}
