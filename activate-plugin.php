<?php
/**
 * Manual Plugin Activation
 * 
 * This file manually activates the plugin if it's not working
 * Access via: /wp-content/plugins/glowess-city-ecommerce/activate-plugin.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('activate_plugins')) {
    die('You do not have permission to activate plugins.');
}

echo '<h1>Manual Plugin Activation</h1>';

// Plugin path
$plugin_file = 'glowess-city-ecommerce/glowess-city-ecommerce.php';

// Get current active plugins
$active_plugins = get_option('active_plugins', array());

echo '<h2>Current Status</h2>';
if (in_array($plugin_file, $active_plugins)) {
    echo '<p style="color: green;">✓ Plugin is already active</p>';
} else {
    echo '<p style="color: red;">✗ Plugin is not active</p>';
}

// Force activation
if (isset($_GET['force_activate'])) {
    echo '<h2>Force Activation</h2>';
    
    // Add to active plugins if not already there
    if (!in_array($plugin_file, $active_plugins)) {
        $active_plugins[] = $plugin_file;
        update_option('active_plugins', $active_plugins);
        echo '<p style="color: green;">✓ Plugin added to active plugins list</p>';
    }
    
    // Load the plugin file
    $plugin_path = WP_PLUGIN_DIR . '/' . $plugin_file;
    if (file_exists($plugin_path)) {
        include_once($plugin_path);
        echo '<p style="color: green;">✓ Plugin file loaded</p>';
    }
    
    // Trigger activation hook
    if (function_exists('glowess_city_ecommerce')) {
        $instance = glowess_city_ecommerce();
        if (method_exists($instance, 'activate')) {
            $instance->activate();
            echo '<p style="color: green;">✓ Activation hook triggered</p>';
        }
    }
    
    // Force WordPress to reload
    echo '<p><strong>Plugin activation completed!</strong></p>';
    echo '<p><a href="' . admin_url('plugins.php') . '">Go to Plugins Page</a></p>';
    echo '<p><a href="' . admin_url('edit.php?post_type=city') . '">Go to Cities</a></p>';
    
} else {
    echo '<h2>Actions</h2>';
    echo '<p><a href="?force_activate=1" style="background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">Force Activate Plugin</a></p>';
}

// Debug info
echo '<h2>Debug Information</h2>';
echo '<p><strong>Plugin File:</strong> ' . $plugin_file . '</p>';
echo '<p><strong>Plugin Path:</strong> ' . WP_PLUGIN_DIR . '/' . $plugin_file . '</p>';
echo '<p><strong>File Exists:</strong> ' . (file_exists(WP_PLUGIN_DIR . '/' . $plugin_file) ? 'Yes' : 'No') . '</p>';

echo '<h3>Active Plugins:</h3>';
echo '<ul>';
foreach ($active_plugins as $plugin) {
    echo '<li>' . $plugin . '</li>';
}
echo '</ul>';

echo '<h3>All Plugins:</h3>';
$all_plugins = get_plugins();
echo '<ul>';
foreach ($all_plugins as $plugin_path => $plugin_data) {
    $status = in_array($plugin_path, $active_plugins) ? 'Active' : 'Inactive';
    echo '<li><strong>' . $plugin_data['Name'] . '</strong> (' . $plugin_path . ') - ' . $status . '</li>';
}
echo '</ul>';
?>
