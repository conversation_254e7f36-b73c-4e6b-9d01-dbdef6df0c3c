# Glowess City E-commerce

WordPress ve WooCommerce için şehir bazlı e-ticaret sistemi. Bu plugin, ürünleri ve kategorileri şehirlere göre organize etmenizi ve müşterilerin şehir bazlı alışveriş yapmasını sağlar.

## Özellikler

### 🏙️ Şehir Yönetimi
- Custom post type olarak şehir ekleme
- Her şehir için 3 adet hero banner görseli
- Şehir başlığı ve açıklaması
- SEO ayarları (meta title, description, keywords)
- Şehir rengi ve görüntüleme ayarları

### 🛍️ Ürün Entegrasyonu
- Ürünlere şehir atama
- Admin panelinde şehir bazlı ürün filtreleme
- Ürün listelerinde şehir bilgisi gösterimi
- Single product sayfasında şehir bilgisi

### 📂 Kategori Entegrasyonu
- Kategorilere şehir atama
- Şehir bazlı kategori yönetimi
- Admin panelinde kategori filtreleme

### 🎯 Frontend Özellikleri
- URL parametresi ile şehir filtreleme (`?city=istanbul`)
- Otomatik ürün ve kategori filtreleme
- Şehir seçici widget
- Hero banner gösterimi
- Google Maps entegrasyonu
- Responsive tasarım

### 🔧 Geliştirici Araçları
- REST API endpoints
- Shortcode desteği
- WordPress hooks ve filters
- AJAX desteği
- Çoklu dil desteği hazır

## Kurulum

1. Plugin dosyalarını `/wp-content/plugins/glowess-city-ecommerce/` klasörüne yükleyin
2. WordPress admin panelinden plugin'i aktifleştirin
3. WooCommerce'in kurulu ve aktif olduğundan emin olun
4. **Şehirler > Yeni Ekle** menüsünden ilk şehrinizi oluşturun

## Kullanım

### Şehir Ekleme
1. **Şehirler > Yeni Ekle** menüsüne gidin
2. Şehir adını girin
3. Açıklama ekleyin
4. 3 adet hero banner görseli yükleyin
5. SEO ayarlarını yapın
6. Yayınlayın

### Ürünlere Şehir Atama
1. Ürün düzenleme sayfasına gidin
2. **Genel** sekmesinde **Şehir** alanını bulun
3. İlgili şehri seçin
4. Ürünü kaydedin

### Kategorilere Şehir Atama
1. **Ürünler > Kategoriler** menüsüne gidin
2. Kategori düzenlerken **Şehir** alanını bulun
3. İlgili şehri seçin
4. Kategoriyi kaydedin

### Frontend Kullanımı
- Mağaza sayfasında şehir seçici kullanın
- URL'ye `?city=sehir-slug` parametresi ekleyin
- Widget alanına şehir seçici widget'ı ekleyin

### Google Maps Kullanımı
1. **Şehirler > Ayarlar** menüsünden Google Maps API anahtarınızı girin
2. Şehir düzenlerken **Şehir Konumları** bölümünden konum ekleyin
3. Sayfalarınızda `[city_maps]` shortcode'unu kullanın
4. Şehir parametresi varsa otomatik olarak o şehrin konumları gösterilir

### Shortcode'lar
- `[city_hero]`: Şehir hero banner'ı gösterir
- `[city_maps]`: Google Maps ile şehir konumlarını gösterir
  - `height`: Harita yüksekliği (varsayılan: 400px)
  - `zoom`: Zoom seviyesi (varsayılan: 12)
  - `city`: Belirli bir şehir (opsiyonel)



## REST API

### Şehir Ürünleri
```
GET /wp-json/glowess-city/v1/city/{slug}/products
```

**Parametreler:**
- `category`: Kategori slug'ı (opsiyonel)
- `limit`: Ürün sayısı (varsayılan: 12)

### Şehir Kategorileri
```
GET /wp-json/glowess-city/v1/city/{slug}/categories
```

## Hooks ve Filters

### Actions
- `glowess_city_before_product_filter`: Ürün filtreleme öncesi
- `glowess_city_after_product_filter`: Ürün filtreleme sonrası
- `glowess_city_before_category_filter`: Kategori filtreleme öncesi
- `glowess_city_after_category_filter`: Kategori filtreleme sonrası

### Filters
- `glowess_city_product_query_args`: Ürün sorgu parametreleri
- `glowess_city_category_query_args`: Kategori sorgu parametreleri
- `glowess_city_selector_cities`: Şehir seçicideki şehirler
- `glowess_city_hero_images`: Hero banner görselleri

## Özelleştirme

### CSS Sınıfları
- `.city-selector-wrapper`: Şehir seçici container
- `.city-hero-section`: Hero banner bölümü
- `.product-city-info`: Ürün şehir bilgisi
- `.city-products-grid`: Ürün grid'i
- `.city-categories-list`: Kategori listesi

### JavaScript Events
- `city_products_filtered`: Ürünler filtrelendiğinde
- `city_categories_loaded`: Kategoriler yüklendiğinde
- `city_changed`: Şehir değiştiğinde

## Sistem Gereksinimleri

- WordPress 5.0+
- WooCommerce 5.0+
- PHP 7.4+
- MySQL 5.6+

## Destek

Sorularınız için:
- GitHub Issues: [Proje Sayfası]
- E-posta: <EMAIL>
- Dokümantasyon: [Wiki Sayfası]

## Lisans

Bu plugin GPL v2 veya üzeri lisansı altında dağıtılmaktadır.

## Changelog

### 1.1.0
- Google Maps entegrasyonu eklendi
- Şehir konumları meta box'ı
- `[city_maps]` shortcode'u
- Responsive harita tasarımı
- Konum pin'leri ve info window'ları

### 1.0.0
- İlk sürüm
- Şehir custom post type
- Ürün ve kategori entegrasyonu
- Frontend filtreleme
- REST API endpoints
- Shortcode desteği
- Admin ayarları

## Katkıda Bulunma

1. Projeyi fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## Teşekkürler

- WordPress Community
- WooCommerce Team
- Tüm katkıda bulunanlar
