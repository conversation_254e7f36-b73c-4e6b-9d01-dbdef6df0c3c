<?php
/**
 * Plugin Name: Glowess City E-commerce
 * Plugin URI: https://glowess.com
 * Description: Şehir bazlı e-ticaret sistemi - WooCommerce entegrasyonu ile şehirlere göre ürün ve kategori yönetimi
 * Version: 1.0.0
 * Author: Glowess
 * Text Domain: glowess-city-ecommerce
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GLOWESS_CITY_ECOMMERCE_VERSION', '1.0.0');
define('GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('GLOWESS_CITY_ECOMMERCE_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GLOWESS_CITY_ECOMMERCE_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class Glowess_City_Ecommerce {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Load components immediately
        add_action('plugins_loaded', array($this, 'load_textdomain'), 1);
        add_action('init', array($this, 'init'), 1);

        // Check if WooCommerce is active
        add_action('admin_init', array($this, 'check_woocommerce_dependency'));

        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load plugin components
        $this->load_includes();
        $this->init_hooks();

        // Force register post type immediately
        $this->register_post_type_immediately();
    }
    
    /**
     * Load plugin includes
     */
    private function load_includes() {
        // Core classes
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-city-post-type.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-city-meta-boxes.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-frontend-handler.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-product-extra-features.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-mobile-bottom-bar.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-city-maps.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-city-info.php';
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'includes/class-city-info-block.php';
        
        // Admin classes - always load for hooks to work
        require_once GLOWESS_CITY_ECOMMERCE_PLUGIN_DIR . 'admin/class-admin-settings.php';
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize components
        new Glowess_City_Post_Type();
        new Glowess_City_Meta_Boxes();
        new Glowess_Frontend_Handler();
        new Glowess_Product_Extra_Features();
        new Glowess_Mobile_Bottom_Bar();
        new Glowess_City_Maps();
        new Glowess_City_Info();
        new Glowess_City_Info_Block();

        // City hero shortcode
        add_shortcode('city_hero', array($this, 'city_hero_shortcode'));

        // City maps shortcode
        add_shortcode('city_maps', array($this, 'city_maps_shortcode'));

        // City info shortcode
        add_shortcode('city_info', array($this, 'city_info_shortcode'));

        // Always initialize admin settings for hooks
        new Glowess_Admin_Settings();

        // Flush rewrite rules on init if needed
        add_action('init', array($this, 'maybe_flush_rewrite_rules'), 999);

        // Rewrite rules removed - using query parameters instead
    }
    
    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'glowess-city-ecommerce',
            false,
            dirname(GLOWESS_CITY_ECOMMERCE_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Check WooCommerce dependency
     */
    public function check_woocommerce_dependency() {
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(GLOWESS_CITY_ECOMMERCE_PLUGIN_BASENAME);
            wp_die(
                __('Glowess City E-commerce requires WooCommerce to be installed and active.', 'glowess-city-ecommerce'),
                __('Plugin Dependency Error', 'glowess-city-ecommerce'),
                array('back_link' => true)
            );
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php 
                echo sprintf(
                    __('Glowess City E-commerce requires %s to be installed and active.', 'glowess-city-ecommerce'),
                    '<strong>WooCommerce</strong>'
                );
                ?>
            </p>
        </div>
        <?php
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables if needed
        $this->create_tables();

        // Register post type first
        $this->register_post_type_immediately();

        // Flush rewrite rules immediately
        flush_rewrite_rules();

        // Set default options
        $this->set_default_options();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        // Add any custom tables if needed in the future
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        // Set default plugin options
        if (!get_option('glowess_city_ecommerce_settings')) {
            add_option('glowess_city_ecommerce_settings', array(
                'enable_city_filter' => 'yes',
                'default_city' => '',
                'show_city_in_product_list' => 'yes'
            ));
        }
    }

    /**
     * Maybe flush rewrite rules
     */
    public function maybe_flush_rewrite_rules() {
        if (get_option('glowess_city_ecommerce_flush_rewrite_rules')) {
            flush_rewrite_rules();
            delete_option('glowess_city_ecommerce_flush_rewrite_rules');
        }
    }

    /**
     * Register post type immediately for debugging
     */
    public function register_post_type_immediately() {
        // Register city post type directly
        $labels = array(
            'name'                  => _x('Şehirler', 'Post type general name', 'glowess-city-ecommerce'),
            'singular_name'         => _x('Şehir', 'Post type singular name', 'glowess-city-ecommerce'),
            'menu_name'             => _x('Şehirler', 'Admin Menu text', 'glowess-city-ecommerce'),
            'name_admin_bar'        => _x('Şehir', 'Add New on Toolbar', 'glowess-city-ecommerce'),
            'add_new'               => __('Yeni Ekle', 'glowess-city-ecommerce'),
            'add_new_item'          => __('Yeni Şehir Ekle', 'glowess-city-ecommerce'),
            'new_item'              => __('Yeni Şehir', 'glowess-city-ecommerce'),
            'edit_item'             => __('Şehir Düzenle', 'glowess-city-ecommerce'),
            'view_item'             => __('Şehir Görüntüle', 'glowess-city-ecommerce'),
            'all_items'             => __('Tüm Şehirler', 'glowess-city-ecommerce'),
            'search_items'          => __('Şehir Ara', 'glowess-city-ecommerce'),
            'parent_item_colon'     => __('Üst Şehir:', 'glowess-city-ecommerce'),
            'not_found'             => __('Şehir bulunamadı.', 'glowess-city-ecommerce'),
            'not_found_in_trash'    => __('Çöp kutusunda şehir bulunamadı.', 'glowess-city-ecommerce'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'show_in_admin_bar'  => true,
            'show_in_nav_menus'  => true,
            'can_export'         => true,
            'query_var'          => false, // Disable to prevent conflict with ?city parameter
            'rewrite'            => array('slug' => 'sehirler'),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => 25,
            'menu_icon'          => 'dashicons-location-alt',
            'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'show_in_rest'       => true,
        );

        register_post_type('city', $args);
    }

    // Rewrite rules removed - using simple query parameters

    /**
     * City hero shortcode
     */
    public function city_hero_shortcode($atts) {
        // City parametresi kontrolü
        $hero_image = 'https://transvelo.github.io/glowess/assets/images/hero-slide.png';
        $hero_subtitle = __('99.5% NATURAL. 100% YOU.', 'glowess');
        $hero_title = __('Beauty You Collection', 'glowess');
        $shop_link = '/shop/';

        if (isset($_GET['city']) && !empty($_GET['city'])) {
            $city_slug = sanitize_text_field($_GET['city']);

            // Şehir bilgilerini al
            $cities = get_posts(array(
                'post_type' => 'city',
                'name' => $city_slug,
                'post_status' => 'publish',
                'posts_per_page' => 1
            ));

            if (!empty($cities)) {
                $city = $cities[0];

                // Mevcut gallery images verisini kullan
                $gallery_images = get_post_meta($city->ID, '_city_gallery_images', true);

                if (!empty($gallery_images) && is_array($gallery_images)) {
                    $first_image = $gallery_images[0]; // İlk görseli al

                    // Görsel ID'sinden URL'yi al
                    if (isset($first_image['id']) && $first_image['id']) {
                        $hero_image = wp_get_attachment_url($first_image['id']);
                    }

                    // Title varsa kullan
                    if (isset($first_image['title']) && !empty($first_image['title'])) {
                        $hero_title = $first_image['title'];
                    } else {
                        $hero_title = $city->post_title . ' ' . __('Beauty Collection', 'glowess');
                    }

                    // Description varsa kullan
                    if (isset($first_image['description']) && !empty($first_image['description'])) {
                        $hero_subtitle = $first_image['description'];
                    } else {
                        $hero_subtitle = sprintf(__('Discover beauty in %s', 'glowess'), $city->post_title);
                    }
                } else {
                    // Gallery images yoksa default değerler
                    $hero_title = $city->post_title . ' ' . __('Beauty Collection', 'glowess');
                    $hero_subtitle = sprintf(__('Discover beauty in %s', 'glowess'), $city->post_title);
                }

                // Shop linkine city parametresi ekle
                $shop_link = '/shop/?city=' . $city_slug;
            }
        }

        ob_start();
        ?>
        <?php if (current_user_can('manage_options') && isset($_GET['debug'])): ?>
            <div style="background: #333; color: white; padding: 10px; margin: 10px 0; font-size: 12px;">
                <strong>City Hero Debug:</strong><br>
                City Slug: <?php echo $city_slug ?? 'None'; ?><br>
                <?php if (isset($city)): ?>
                    City ID: <?php echo $city->ID; ?><br>
                    City Title: <?php echo $city->post_title; ?><br>
                    <strong>Meta Fields:</strong><br>
                    _city_banner_image: <?php echo get_post_meta($city->ID, '_city_banner_image', true) ?: 'EMPTY'; ?><br>
                    _city_banner_title: <?php echo get_post_meta($city->ID, '_city_banner_title', true) ?: 'EMPTY'; ?><br>
                    _city_banner_description: <?php echo get_post_meta($city->ID, '_city_banner_description', true) ?: 'EMPTY'; ?><br>
                    <strong>All Meta:</strong><br>
                    <?php
                    $all_meta = get_post_meta($city->ID);
                    foreach ($all_meta as $key => $value) {
                        if (strpos($key, 'banner') !== false || strpos($key, 'city') !== false) {
                            echo $key . ': ' . (is_array($value) ? implode(', ', $value) : $value) . '<br>';
                        }
                    }
                    ?>
                <?php endif; ?>
                <strong>Final Values:</strong><br>
                Hero Image: <?php echo $hero_image; ?><br>
                Hero Title: <?php echo $hero_title; ?><br>
                Hero Subtitle: <?php echo $hero_subtitle; ?><br>
                Shop Link: <?php echo $shop_link; ?>
            </div>
        <?php endif; ?>

        <div class="wp-block-group alignfull city-hero-banner">
            <div class="wp-block-cover alignfull is-light has-custom-content-position is-position-bottom-center gradient height-img min-height-img" style="padding-top:var(--wp--preset--spacing--50);padding-bottom:var(--wp--preset--spacing--50);min-height:960px">
                <span aria-hidden="true" class="wp-block-cover__background has-background-dim-0 has-background-dim has-background-gradient" style="background:linear-gradient(180deg,rgba(0,0,0,0) 0%,rgba(0,0,0,0.3) 100%)"></span>
                <img class="wp-block-cover__image-background" alt="" src="<?php echo esc_url($hero_image); ?>" style="object-position:46% 0%" data-object-fit="cover" data-object-position="46% 0%"/>

                <div class="wp-block-cover__inner-container">
                    <div class="wp-block-group" style="margin-top:7px;margin-bottom:7px;padding-top:var(--wp--preset--spacing--20);padding-bottom:var(--wp--preset--spacing--20)">
                        <p class="has-text-align-center has-base-color has-text-color has-link-color" style="font-size:16px;font-style:normal;font-weight:400;line-height:1.7">
                            <?php echo esc_html($hero_subtitle); ?>
                        </p>
                        <h2 class="wp-block-heading has-text-align-center has-base-color has-text-color has-link-color" style="margin-top:10px;font-size:80px;font-style:normal;font-weight:500;line-height:1.2">
                            <?php echo esc_html($hero_title); ?>
                        </h2>
                        <div class="wp-block-buttons" style="justify-content:center;">
                            <div class="wp-block-button has-custom-font-size inline-img" style="font-size:15px;font-style:normal;font-weight:400;line-height:1.2">
                                <a class="wp-block-button__link has-secondary-color has-base-background-color has-text-color has-background has-link-color wp-element-button" href="<?php echo esc_url($shop_link); ?>" style="border-style:none;border-width:0px;border-radius:0px;padding-top:13px;padding-right:50px;padding-bottom:13px;padding-left:50px">
                                    <?php echo esc_html__('SHOP NOW', 'glowess'); ?>
                                    <img class="wp-image-15" style="width: 14px;" src="<?php echo esc_url(get_template_directory_uri()) . '/assets/images/right-up-arrow.svg'; ?>" alt="">
                                </a>
                            </div>
                        </div>

                        <!-- Aşağı Ok -->
                        <div class="city-hero-scroll-arrow" style="text-align: center; margin-top: 40px;">
                            <button onclick="scrollDown()" style="background: none; border: none; cursor: pointer; padding: 10px; transition: all 0.3s ease;">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="opacity: 0.8;">
                                    <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .city-hero-scroll-arrow button:hover {
            transform: translateY(5px);
        }
        .city-hero-scroll-arrow svg {
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        </style>

        <script>
        function scrollDown() {
            window.scrollBy({
                top: 700,
                behavior: 'smooth'
            });
        }
        </script>

        <?php
        return ob_get_clean();
    }

    /**
     * City maps shortcode
     */
    public function city_maps_shortcode($atts) {
        // Delegate to the maps class
        if (class_exists('Glowess_City_Maps')) {
            $maps_instance = new Glowess_City_Maps();
            return $maps_instance->render_maps_shortcode($atts);
        }
        return '';
    }

    /**
     * City info shortcode
     */
    public function city_info_shortcode($atts) {
        // Delegate to the info class
        if (class_exists('Glowess_City_Info')) {
            $info_instance = new Glowess_City_Info();
            return $info_instance->render_info_shortcode($atts);
        }
        return '';
    }
}

// Initialize the plugin
function glowess_city_ecommerce() {
    return Glowess_City_Ecommerce::get_instance();
}

// Start the plugin
glowess_city_ecommerce();
