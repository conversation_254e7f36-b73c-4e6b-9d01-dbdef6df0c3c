/**
 * Glowess City Maps JavaScript
 */

(function($) {
    'use strict';
    
    // Global maps object
    window.GlowessCityMaps = {
        maps: {},
        markers: {},
        infoWindows: {},
        
        /**
         * Initialize all maps on the page
         */
        init: function() {
            if (typeof google === 'undefined' || !google.maps) {
                console.warn('Google Maps API not loaded');
                return;
            }
            
            // Initialize maps when DOM is ready
            $(document).ready(function() {
                $('.city-map').each(function() {
                    var mapId = $(this).attr('id');
                    if (mapId) {
                        GlowessCityMaps.initMap(mapId);
                    }
                });
            });
        },
        
        /**
         * Initialize a single map
         */
        initMap: function(mapId) {
            var mapElement = document.getElementById(mapId);
            if (!mapElement) {
                return;
            }
            
            // Default map options
            var mapOptions = {
                zoom: 12,
                center: { lat: 41.0082, lng: 28.9784 }, // Istanbul default
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: this.getMapStyles(),
                zoomControl: true,
                mapTypeControl: false,
                scaleControl: true,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: true
            };
            
            // Create map
            var map = new google.maps.Map(mapElement, mapOptions);
            this.maps[mapId] = map;
            
            // Initialize markers array for this map
            this.markers[mapId] = [];
            
            // Add resize listener
            google.maps.event.addDomListener(window, 'resize', function() {
                google.maps.event.trigger(map, 'resize');
            });
            
            return map;
        },
        
        /**
         * Add markers to a map
         */
        addMarkers: function(mapId, locations) {
            var map = this.maps[mapId];
            if (!map || !locations || locations.length === 0) {
                return;
            }
            
            var bounds = new google.maps.LatLngBounds();
            var infoWindow = new google.maps.InfoWindow();
            this.infoWindows[mapId] = infoWindow;
            
            locations.forEach(function(location, index) {
                if (!location.lat || !location.lng) {
                    return;
                }
                
                var position = new google.maps.LatLng(location.lat, location.lng);
                
                var marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: location.title || 'Konum ' + (index + 1),
                    animation: google.maps.Animation.DROP,
                    icon: GlowessCityMaps.getMarkerIcon(index)
                });
                
                // Add to markers array
                GlowessCityMaps.markers[mapId].push(marker);
                
                // Extend bounds
                bounds.extend(position);
                
                // Create info window content
                var infoContent = GlowessCityMaps.createInfoWindowContent(location, index);
                
                // Add click listener
                marker.addListener('click', function() {
                    infoWindow.setContent(infoContent);
                    infoWindow.open(map, marker);
                });
                
                // Add hover effect
                marker.addListener('mouseover', function() {
                    marker.setAnimation(google.maps.Animation.BOUNCE);
                    setTimeout(function() {
                        marker.setAnimation(null);
                    }, 750);
                });
            });
            
            // Fit bounds if multiple markers
            if (locations.length > 1) {
                map.fitBounds(bounds);
                
                // Ensure minimum zoom
                google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                    if (map.getZoom() > 15) {
                        map.setZoom(15);
                    }
                });
            } else if (locations.length === 1) {
                map.setCenter(new google.maps.LatLng(locations[0].lat, locations[0].lng));
            }
        },
        
        /**
         * Create info window content
         */
        createInfoWindowContent: function(location, index) {
            var content = '<div class="city-map-info">';
            
            if (location.title) {
                content += '<h4>' + this.escapeHtml(location.title) + '</h4>';
            }
            
            if (location.contact) {
                content += '<p>' + this.escapeHtml(location.contact).replace(/\n/g, '<br>') + '</p>';
            }
            
            content += '<div class="location-coordinates">';
            content += '<small>Lat: ' + location.lat + ', Lng: ' + location.lng + '</small>';
            content += '</div>';
            
            // Add directions link
            content += '<div class="location-actions" style="margin-top: 8px;">';
            content += '<a href="https://www.google.com/maps/dir/?api=1&destination=' + 
                      location.lat + ',' + location.lng + '" target="_blank" ' +
                      'style="color: #1a73e8; text-decoration: none; font-size: 12px;">Yol Tarifi Al</a>';
            content += '</div>';
            
            content += '</div>';
            
            return content;
        },
        
        /**
         * Get custom marker icon
         */
        getMarkerIcon: function(index) {
            return {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(
                    '<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">' +
                    '<circle cx="16" cy="16" r="12" fill="#0073aa" stroke="#ffffff" stroke-width="2"/>' +
                    '<text x="16" y="20" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">' +
                    (index + 1) + '</text></svg>'
                ),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
            };
        },
        
        /**
         * Get map styles
         */
        getMapStyles: function() {
            return [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                },
                {
                    featureType: 'transit',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                },
                {
                    featureType: 'road',
                    elementType: 'labels.icon',
                    stylers: [{ visibility: 'off' }]
                }
            ];
        },
        
        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        },
        
        /**
         * Get map by ID
         */
        getMap: function(mapId) {
            return this.maps[mapId];
        },
        
        /**
         * Get markers by map ID
         */
        getMarkers: function(mapId) {
            return this.markers[mapId] || [];
        },
        
        /**
         * Clear all markers from a map
         */
        clearMarkers: function(mapId) {
            var markers = this.markers[mapId];
            if (markers) {
                markers.forEach(function(marker) {
                    marker.setMap(null);
                });
                this.markers[mapId] = [];
            }
        },
        
        /**
         * Resize map
         */
        resizeMap: function(mapId) {
            var map = this.maps[mapId];
            if (map) {
                google.maps.event.trigger(map, 'resize');
            }
        },
        
        /**
         * Center map on location
         */
        centerMap: function(mapId, lat, lng, zoom) {
            var map = this.maps[mapId];
            if (map) {
                map.setCenter(new google.maps.LatLng(lat, lng));
                if (zoom) {
                    map.setZoom(zoom);
                }
            }
        }
    };
    
    // Initialize when Google Maps API is loaded
    if (typeof google !== 'undefined' && google.maps) {
        GlowessCityMaps.init();
    } else {
        // Wait for Google Maps API to load
        window.addEventListener('load', function() {
            if (typeof google !== 'undefined' && google.maps) {
                GlowessCityMaps.init();
            }
        });
    }
    
})(jQuery);
