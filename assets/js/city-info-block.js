(function() {
    'use strict';
    
    const { registerBlockType } = wp.blocks;
    const { InspectorControls } = wp.blockEditor;
    const { PanelBody, SelectControl, ToggleControl, Placeholder, Spinner } = wp.components;
    const { Component, Fragment } = wp.element;
    
    class CityInfoBlock extends Component {
        constructor(props) {
            super(props);
            this.state = {
                cities: [],
                loading: true,
                preview: ''
            };
        }
        
        componentDidMount() {
            this.loadCities();
            this.updatePreview();
        }
        
        componentDidUpdate(prevProps) {
            if (prevProps.attributes !== this.props.attributes) {
                this.updatePreview();
            }
        }
        
        loadCities() {
            const data = new FormData();
            data.append('action', 'get_cities_list');
            data.append('nonce', window.cityInfoBlock ? window.cityInfoBlock.nonce : '');

            fetch(window.cityInfoBlock ? window.cityInfoBlock.ajaxUrl : '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: data
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    this.setState({
                        cities: [
                            { value: '', label: 'URL parametresini kullan' },
                            ...result.data
                        ],
                        loading: false
                    });
                } else {
                    this.setState({ loading: false });
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                this.setState({ loading: false });
            });
        }
        
        updatePreview() {
            const { attributes } = this.props;
            const { citySlug, showTitle, showDescription, useUrlParameter } = attributes;

            let preview = '';

            if (useUrlParameter && !citySlug) {
                preview = 'URL parametresinden şehir bilgisi gösterilecek (?city=sehir-slug)';
            } else if (citySlug) {
                const selectedCity = this.state.cities.find(city => city.value === citySlug);
                if (selectedCity) {
                    preview = 'Şehir: ' + selectedCity.label;

                    const showParts = [];
                    if (showTitle) showParts.push('Başlık');
                    if (showDescription) showParts.push('Açıklama');

                    if (showParts.length > 0) {
                        preview += '\nGösterilecek: ' + showParts.join(', ');
                    }
                }
            } else {
                preview = 'Şehir seçin veya URL parametresi kullanın';
            }

            this.setState({ preview });
        }
        
        render() {
            const { attributes, setAttributes } = this.props;
            const { citySlug, showTitle, showDescription, useUrlParameter } = attributes;
            const { cities, loading, preview } = this.state;
            
            return (
                <Fragment>
                    <InspectorControls>
                        <PanelBody title="Şehir Ayarları" initialOpen={true}>
                            <ToggleControl
                                label="URL parametresini kullan"
                                help="Aktifse ?city=sehir-slug parametresini kullanır"
                                checked={useUrlParameter}
                                onChange={(value) => setAttributes({ useUrlParameter: value })}
                            />

                            {!loading && (
                                <SelectControl
                                    label="Şehir Seçin"
                                    help={useUrlParameter ? "URL parametresi yoksa bu şehir gösterilir" : "Gösterilecek şehir"}
                                    value={citySlug}
                                    options={cities}
                                    onChange={(value) => setAttributes({ citySlug: value })}
                                />
                            )}
                        </PanelBody>

                        <PanelBody title="Gösterim Ayarları" initialOpen={true}>
                            <ToggleControl
                                label="Başlığı Göster"
                                checked={showTitle}
                                onChange={(value) => setAttributes({ showTitle: value })}
                            />

                            <ToggleControl
                                label="Açıklamayı Göster"
                                checked={showDescription}
                                onChange={(value) => setAttributes({ showDescription: value })}
                            />
                        </PanelBody>
                    </InspectorControls>
                    
                    <div className="city-info-block-editor">
                        {loading ? (
                            <Placeholder
                                icon="location-alt"
                                label="Şehir Bilgileri"
                            >
                                <Spinner />
                                <p>Şehirler yükleniyor...</p>
                            </Placeholder>
                        ) : (
                            <Placeholder
                                icon="location-alt"
                                label="Şehir Bilgileri"
                                instructions="Sağ panelden ayarları yapılandırın"
                            >
                                <div className="city-info-preview">
                                    <strong>Önizleme:</strong>
                                    <pre style={{ whiteSpace: 'pre-wrap', marginTop: '10px', fontSize: '14px' }}>
                                        {preview}
                                    </pre>
                                </div>
                            </Placeholder>
                        )}
                    </div>
                </Fragment>
            );
        }
    }
    
    registerBlockType('glowess/city-info', {
        title: 'Şehir Bilgileri',
        description: 'Şehir başlığı ve açıklamasını gösterir',
        icon: 'location-alt',
        category: 'widgets',
        keywords: [
            'şehir',
            'city',
            'bilgi',
            'info'
        ],
        supports: {
            align: ['wide', 'full'],
            html: false
        },
        attributes: {
            citySlug: {
                type: 'string',
                default: ''
            },
            showTitle: {
                type: 'boolean',
                default: true
            },
            showDescription: {
                type: 'boolean',
                default: true
            },
            useUrlParameter: {
                type: 'boolean',
                default: true
            }
        },
        edit: CityInfoBlock,
        save: function() {
            // Server-side rendering kullanıyoruz
            return null;
        }
    });
})();
