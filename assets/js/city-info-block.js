(() => {
    'use strict';

    const { registerBlockType } = wp.blocks;
    const { InspectorControls } = wp.blockEditor;
    const { PanelBody, SelectControl, ToggleControl, Placeholder, Spinner } = wp.components;
    const { useState, useEffect } = wp.element;

    const CityInfoEdit = (props) => {
        const { attributes, setAttributes } = props;
        const { citySlug, showTitle, showDescription, useUrlParameter } = attributes;

        const [cities, setCities] = useState([]);
        const [loading, setLoading] = useState(true);
        const [preview, setPreview] = useState('');

        // Load cities on component mount
        useEffect(() => {
            loadCities();
        }, []);

        // Update preview when attributes change
        useEffect(() => {
            updatePreview();
        }, [attributes, cities]);

        const loadCities = async () => {
            try {
                const formData = new FormData();
                formData.append('action', 'get_cities_list');
                formData.append('nonce', window.cityInfoBlock?.nonce || '');

                const response = await fetch(window.cityInfoBlock?.ajaxUrl || '/wp-admin/admin-ajax.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    setCities([
                        { value: '', label: 'URL parametresini kullan' },
                        ...result.data
                    ]);
                }
            } catch (error) {
                console.error('Error loading cities:', error);
            } finally {
                setLoading(false);
            }
        };

        const updatePreview = () => {
            let newPreview = '';

            if (useUrlParameter && !citySlug) {
                newPreview = 'URL parametresinden şehir bilgisi gösterilecek (?city=sehir-slug)';
            } else if (citySlug) {
                const selectedCity = cities.find(city => city.value === citySlug);
                if (selectedCity) {
                    newPreview = `Şehir: ${selectedCity.label}`;

                    const showParts = [];
                    if (showTitle) showParts.push('Başlık');
                    if (showDescription) showParts.push('Açıklama');

                    if (showParts.length > 0) {
                        newPreview += `\nGösterilecek: ${showParts.join(', ')}`;
                    }
                }
            } else {
                newPreview = 'Şehir seçin veya URL parametresi kullanın';
            }

            setPreview(newPreview);
        };

        return (
            <>
                <InspectorControls>
                    <PanelBody title="Şehir Ayarları" initialOpen={true}>
                        <ToggleControl
                            label="URL parametresini kullan"
                            help="Aktifse ?city=sehir-slug parametresini kullanır"
                            checked={useUrlParameter}
                            onChange={(value) => setAttributes({ useUrlParameter: value })}
                        />

                        {!loading && (
                            <SelectControl
                                label="Şehir Seçin"
                                help={useUrlParameter ? "URL parametresi yoksa bu şehir gösterilir" : "Gösterilecek şehir"}
                                value={citySlug}
                                options={cities}
                                onChange={(value) => setAttributes({ citySlug: value })}
                            />
                        )}
                    </PanelBody>

                    <PanelBody title="Gösterim Ayarları" initialOpen={true}>
                        <ToggleControl
                            label="Başlığı Göster"
                            checked={showTitle}
                            onChange={(value) => setAttributes({ showTitle: value })}
                        />

                        <ToggleControl
                            label="Açıklamayı Göster"
                            checked={showDescription}
                            onChange={(value) => setAttributes({ showDescription: value })}
                        />
                    </PanelBody>
                </InspectorControls>

                <div className="city-info-block-editor">
                    {loading ? (
                        <Placeholder
                            icon="location-alt"
                            label="Şehir Bilgileri"
                        >
                            <Spinner />
                            <p>Şehirler yükleniyor...</p>
                        </Placeholder>
                    ) : (
                        <Placeholder
                            icon="location-alt"
                            label="Şehir Bilgileri"
                            instructions="Sağ panelden ayarları yapılandırın"
                        >
                            <div className="city-info-preview">
                                <strong>Önizleme:</strong>
                                <pre style={{
                                    whiteSpace: 'pre-wrap',
                                    marginTop: '10px',
                                    fontSize: '14px',
                                    background: '#f0f0f0',
                                    padding: '10px',
                                    borderRadius: '4px'
                                }}>
                                    {preview}
                                </pre>
                            </div>
                        </Placeholder>
                    )}
                </div>
            </>
        );
    };

    registerBlockType('glowess/city-info', {
        apiVersion: 2,
        title: 'Şehir Bilgileri',
        description: 'Şehir başlığı ve açıklamasını gösterir',
        icon: 'location-alt',
        category: 'widgets',
        keywords: ['şehir', 'city', 'bilgi', 'info'],
        supports: {
            align: ['wide', 'full'],
            html: false
        },
        attributes: {
            citySlug: {
                type: 'string',
                default: ''
            },
            showTitle: {
                type: 'boolean',
                default: true
            },
            showDescription: {
                type: 'boolean',
                default: true
            },
            useUrlParameter: {
                type: 'boolean',
                default: true
            }
        },
        edit: CityInfoEdit,
        save: () => null // Server-side rendering
    });
})();
