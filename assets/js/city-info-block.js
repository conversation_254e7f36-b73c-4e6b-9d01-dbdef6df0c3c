(function() {
    'use strict';
    
    const { registerBlockType } = wp.blocks;
    const { InspectorControls } = wp.blockEditor;
    const { PanelBody, SelectControl, ToggleControl, Placeholder, Spinner } = wp.components;
    const { Component, Fragment } = wp.element;
    const { __ } = wp.i18n;
    
    class CityInfoBlock extends Component {
        constructor(props) {
            super(props);
            this.state = {
                cities: [],
                loading: true,
                preview: ''
            };
        }
        
        componentDidMount() {
            this.loadCities();
            this.updatePreview();
        }
        
        componentDidUpdate(prevProps) {
            if (prevProps.attributes !== this.props.attributes) {
                this.updatePreview();
            }
        }
        
        loadCities() {
            const data = new FormData();
            data.append('action', 'get_cities_list');
            data.append('nonce', cityInfoBlock.nonce);
            
            fetch(cityInfoBlock.ajaxUrl, {
                method: 'POST',
                body: data
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    this.setState({
                        cities: [
                            { value: '', label: __('URL parametresini kullan', 'glowess-city-ecommerce') },
                            ...result.data
                        ],
                        loading: false
                    });
                } else {
                    this.setState({ loading: false });
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                this.setState({ loading: false });
            });
        }
        
        updatePreview() {
            const { attributes } = this.props;
            const { citySlug, showTitle, showDescription, useUrlParameter } = attributes;
            
            let preview = '';
            
            if (useUrlParameter && !citySlug) {
                preview = __('URL parametresinden şehir bilgisi gösterilecek (?city=sehir-slug)', 'glowess-city-ecommerce');
            } else if (citySlug) {
                const selectedCity = this.state.cities.find(city => city.value === citySlug);
                if (selectedCity) {
                    preview = __('Şehir: ', 'glowess-city-ecommerce') + selectedCity.label;
                    
                    const showParts = [];
                    if (showTitle) showParts.push(__('Başlık', 'glowess-city-ecommerce'));
                    if (showDescription) showParts.push(__('Açıklama', 'glowess-city-ecommerce'));
                    
                    if (showParts.length > 0) {
                        preview += '\n' + __('Gösterilecek: ', 'glowess-city-ecommerce') + showParts.join(', ');
                    }
                }
            } else {
                preview = __('Şehir seçin veya URL parametresi kullanın', 'glowess-city-ecommerce');
            }
            
            this.setState({ preview });
        }
        
        render() {
            const { attributes, setAttributes } = this.props;
            const { citySlug, showTitle, showDescription, useUrlParameter } = attributes;
            const { cities, loading, preview } = this.state;
            
            return (
                <Fragment>
                    <InspectorControls>
                        <PanelBody title={__('Şehir Ayarları', 'glowess-city-ecommerce')} initialOpen={true}>
                            <ToggleControl
                                label={__('URL parametresini kullan', 'glowess-city-ecommerce')}
                                help={__('Aktifse ?city=sehir-slug parametresini kullanır', 'glowess-city-ecommerce')}
                                checked={useUrlParameter}
                                onChange={(value) => setAttributes({ useUrlParameter: value })}
                            />
                            
                            {!loading && (
                                <SelectControl
                                    label={__('Şehir Seçin', 'glowess-city-ecommerce')}
                                    help={useUrlParameter ? __('URL parametresi yoksa bu şehir gösterilir', 'glowess-city-ecommerce') : __('Gösterilecek şehir', 'glowess-city-ecommerce')}
                                    value={citySlug}
                                    options={cities}
                                    onChange={(value) => setAttributes({ citySlug: value })}
                                />
                            )}
                        </PanelBody>
                        
                        <PanelBody title={__('Gösterim Ayarları', 'glowess-city-ecommerce')} initialOpen={true}>
                            <ToggleControl
                                label={__('Başlığı Göster', 'glowess-city-ecommerce')}
                                checked={showTitle}
                                onChange={(value) => setAttributes({ showTitle: value })}
                            />
                            
                            <ToggleControl
                                label={__('Açıklamayı Göster', 'glowess-city-ecommerce')}
                                checked={showDescription}
                                onChange={(value) => setAttributes({ showDescription: value })}
                            />
                        </PanelBody>
                    </InspectorControls>
                    
                    <div className="city-info-block-editor">
                        {loading ? (
                            <Placeholder
                                icon="location-alt"
                                label={__('Şehir Bilgileri', 'glowess-city-ecommerce')}
                            >
                                <Spinner />
                                <p>{__('Şehirler yükleniyor...', 'glowess-city-ecommerce')}</p>
                            </Placeholder>
                        ) : (
                            <Placeholder
                                icon="location-alt"
                                label={__('Şehir Bilgileri', 'glowess-city-ecommerce')}
                                instructions={__('Sağ panelden ayarları yapılandırın', 'glowess-city-ecommerce')}
                            >
                                <div className="city-info-preview">
                                    <strong>{__('Önizleme:', 'glowess-city-ecommerce')}</strong>
                                    <pre style={{ whiteSpace: 'pre-wrap', marginTop: '10px', fontSize: '14px' }}>
                                        {preview}
                                    </pre>
                                </div>
                            </Placeholder>
                        )}
                    </div>
                </Fragment>
            );
        }
    }
    
    registerBlockType('glowess/city-info', {
        title: __('Şehir Bilgileri', 'glowess-city-ecommerce'),
        description: __('Şehir başlığı ve açıklamasını gösterir', 'glowess-city-ecommerce'),
        icon: 'location-alt',
        category: 'widgets',
        keywords: [
            __('şehir', 'glowess-city-ecommerce'),
            __('city', 'glowess-city-ecommerce'),
            __('bilgi', 'glowess-city-ecommerce'),
            __('info', 'glowess-city-ecommerce')
        ],
        supports: {
            align: ['wide', 'full'],
            html: false
        },
        attributes: {
            citySlug: {
                type: 'string',
                default: ''
            },
            showTitle: {
                type: 'boolean',
                default: true
            },
            showDescription: {
                type: 'boolean',
                default: true
            },
            useUrlParameter: {
                type: 'boolean',
                default: true
            }
        },
        edit: CityInfoBlock,
        save: function() {
            // Server-side rendering kullanıyoruz
            return null;
        }
    });
})();
