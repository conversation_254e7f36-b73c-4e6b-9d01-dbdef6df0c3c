/**
 * Glowess City Info Styles
 */

/* Main Container */
.city-info-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Title Section */
.city-info-title-section {
    margin-bottom: 25px;
    text-align: center;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 20px;
}

.city-info-title {
    font-size: 2.5em;
    color: #2c3e50;
    margin: 0;
    font-weight: 700;
    line-height: 1.2;
}

/* Description Section */
.city-info-description-section {
    margin-bottom: 20px;
}

.city-info-description {
    font-size: 1.1em;
    color: #555;
    line-height: 1.7;
    text-align: justify;
}

.city-info-description h1,
.city-info-description h2,
.city-info-description h3,
.city-info-description h4,
.city-info-description h5,
.city-info-description h6 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
}

.city-info-description p {
    margin-bottom: 15px;
}

.city-info-description ul,
.city-info-description ol {
    margin: 15px 0;
    padding-left: 30px;
}

.city-info-description li {
    margin-bottom: 8px;
}

.city-info-description a {
    color: #0073aa;
    text-decoration: none;
    transition: color 0.2s ease;
}

.city-info-description a:hover {
    color: #005a87;
    text-decoration: underline;
}

.city-info-description img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.city-info-description blockquote {
    background: #f8f9fa;
    border-left: 4px solid #0073aa;
    margin: 20px 0;
    padding: 15px 20px;
    font-style: italic;
    color: #666;
}



/* Notice Messages */
.city-info-notice {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
    font-weight: 500;
}

/* Gutenberg Block Styles */
.city-info-block-editor {
    margin: 20px 0;
}

.city-info-block-editor .components-placeholder {
    min-height: 120px;
}

.city-info-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
    text-align: left;
}

.city-info-preview pre {
    margin: 0;
    color: #495057;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Block specific styles */
.city-info-block {
    border-left: 4px solid #0073aa;
    padding-left: 20px;
}

.city-info-block .city-info-title {
    color: #0073aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .city-info-container {
        padding: 20px;
        margin: 15px 0;
    }

    .city-info-title {
        font-size: 2em;
    }

    .city-info-description {
        font-size: 1em;
        text-align: left;
    }
}

@media (max-width: 480px) {
    .city-info-container {
        padding: 15px;
        border-radius: 8px;
    }

    .city-info-title {
        font-size: 1.8em;
    }

    .city-info-description {
        font-size: 0.95em;
    }
}

/* Print Styles */
@media print {
    .city-info-container {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }

    .city-info-title,
    .city-info-description {
        color: black !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .city-info-container {
        background: #2c2c2c;
        color: #e0e0e0;
    }

    .city-info-title {
        color: #ffffff;
    }

    .city-info-description {
        color: #cccccc;
    }

    .city-info-description h1,
    .city-info-description h2,
    .city-info-description h3,
    .city-info-description h4,
    .city-info-description h5,
    .city-info-description h6 {
        color: #ffffff;
    }

    .city-info-description a {
        color: #4fc3f7;
    }

    .city-info-description a:hover {
        color: #29b6f6;
    }

    .city-info-description blockquote {
        background: #3a3a3a;
        color: #cccccc;
        border-left-color: #4fc3f7;
    }

    .city-info-notice {
        background: #1a2d3d;
        color: #ccddff;
        border-color: #2d4a5a;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .city-info-container {
        border: 2px solid #000;
    }

    .city-info-title,
    .city-info-description {
        color: #000 !important;
    }
}
