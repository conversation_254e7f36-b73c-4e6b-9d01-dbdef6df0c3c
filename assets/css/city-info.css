/**
 * Glowess City Info Styles
 */

/* Main Container */
.city-info-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.city-info-header {
    margin-bottom: 30px;
    text-align: center;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 20px;
}

.city-info-title {
    font-size: 2.5em;
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-weight: 700;
    text-transform: capitalize;
}

.city-info-description {
    font-size: 1.1em;
    color: #666;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
}

/* Section Headings */
.city-info-container h3 {
    font-size: 1.4em;
    color: #0073aa;
    margin: 25px 0 15px 0;
    font-weight: 600;
    border-left: 4px solid #0073aa;
    padding-left: 15px;
}

/* Stats Grid */
.city-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.city-stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #0073aa;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.city-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-label {
    display: block;
    font-weight: 600;
    color: #555;
    font-size: 0.9em;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-size: 1.1em;
    color: #2c3e50;
    font-weight: 500;
}

/* Contact Grid */
.city-contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.city-contact-item {
    background: #e8f4f8;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
    transition: transform 0.2s ease;
}

.city-contact-item:hover {
    transform: translateY(-1px);
}

.contact-label {
    display: block;
    font-weight: 600;
    color: #0c5460;
    font-size: 0.9em;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.contact-value {
    display: block;
    font-size: 1em;
    color: #2c3e50;
    font-weight: 500;
}

.contact-value a {
    color: #0073aa;
    text-decoration: none;
    transition: color 0.2s ease;
}

.contact-value a:hover {
    color: #005a87;
    text-decoration: underline;
}

/* Highlights */
.city-highlights-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.highlight-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 25px;
    font-weight: 500;
    text-align: center;
    position: relative;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.highlight-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.highlight-item:before {
    content: "✨";
    margin-right: 8px;
}

/* Compact Style */
.city-info-style-compact {
    padding: 20px;
    margin: 15px 0;
}

.city-info-style-compact .city-info-title {
    font-size: 2em;
    margin-bottom: 10px;
}

.city-info-style-compact .city-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.city-info-style-compact .city-stat-item,
.city-info-style-compact .city-contact-item {
    padding: 10px;
}

.city-info-style-compact .city-highlights-list {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Detailed Style */
.city-info-style-detailed {
    padding: 40px;
}

.city-info-style-detailed .city-info-title {
    font-size: 3em;
}

.city-info-style-detailed .city-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.city-info-style-detailed .city-stat-item,
.city-info-style-detailed .city-contact-item {
    padding: 20px;
}

/* Notice Messages */
.city-info-notice {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    border-radius: 5px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .city-info-container {
        padding: 20px;
        margin: 15px 0;
    }
    
    .city-info-title {
        font-size: 2em;
    }
    
    .city-stats-grid,
    .city-contact-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .city-highlights-list {
        grid-template-columns: 1fr;
    }
    
    .city-info-container h3 {
        font-size: 1.2em;
        padding-left: 10px;
    }
}

@media (max-width: 480px) {
    .city-info-container {
        padding: 15px;
        border-radius: 8px;
    }
    
    .city-info-title {
        font-size: 1.8em;
    }
    
    .city-stat-item,
    .city-contact-item {
        padding: 12px;
    }
    
    .highlight-item {
        padding: 12px 16px;
        font-size: 0.9em;
    }
}

/* Print Styles */
@media print {
    .city-info-container {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }
    
    .city-stat-item,
    .city-contact-item,
    .highlight-item {
        break-inside: avoid;
        background: white !important;
        border: 1px solid #ddd;
        color: black !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .city-info-container {
        background: #2c2c2c;
        color: #e0e0e0;
    }
    
    .city-info-title {
        color: #ffffff;
    }
    
    .city-info-description {
        color: #cccccc;
    }
    
    .city-info-container h3 {
        color: #4fc3f7;
        border-left-color: #4fc3f7;
    }
    
    .city-stat-item {
        background: #3a3a3a;
        color: #e0e0e0;
    }
    
    .stat-label {
        color: #cccccc;
    }
    
    .stat-value {
        color: #ffffff;
    }
    
    .city-contact-item {
        background: #2a3a4a;
    }
    
    .contact-label {
        color: #4fc3f7;
    }
    
    .contact-value {
        color: #ffffff;
    }
    
    .contact-value a {
        color: #4fc3f7;
    }
    
    .contact-value a:hover {
        color: #29b6f6;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .city-info-container {
        border: 2px solid #000;
    }
    
    .city-stat-item,
    .city-contact-item,
    .highlight-item {
        border: 1px solid #000;
    }
    
    .city-info-container h3 {
        border-left: 6px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .city-stat-item,
    .city-contact-item,
    .highlight-item {
        transition: none;
    }
    
    .city-stat-item:hover,
    .city-contact-item:hover,
    .highlight-item:hover {
        transform: none;
    }
}
