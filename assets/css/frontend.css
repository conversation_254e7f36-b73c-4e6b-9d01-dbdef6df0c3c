/**
 * Glowess City E-commerce Frontend Styles
 */

/* City Selector */
.city-selector-wrapper {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.city-selector-form {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.city-selector-form label {
    font-weight: 600;
    margin-bottom: 0;
}

.city-selector {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    min-width: 200px;
}

.city-selector:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.2);
}

/* City Widget */
.city-widget-form {
    margin: 0;
}

.city-widget-selector {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
}

/* Product City Info */
.product-city-info {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-left: 4px solid #0073aa;
    border-radius: 0 4px 4px 0;
}

.product-city-info h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.product-city-info p {
    margin: 0 0 10px 0;
}

.product-city-info a {
    color: #0073aa;
    text-decoration: none;
    font-weight: 600;
}

.product-city-info a:hover {
    text-decoration: underline;
}

.city-preview-image {
    margin-top: 10px;
}

.city-preview-image img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

/* Product City in Loop */
.product-city-loop {
    margin: 5px 0;
    font-size: 12px;
    color: #666;
}

.product-city-loop small {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* City Hero Section */
.city-hero-section {
    position: relative;
    margin-bottom: 30px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
}

.city-hero-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.city-hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.city-hero-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 40px 20px 20px;
}

.city-hero-content h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.city-hero-content .city-description {
    font-size: 1.1em;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* City Info Shortcode */
.city-info {
    margin: 20px 0;
}

.city-info h2 {
    color: #0073aa;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.city-description {
    margin-bottom: 30px;
    line-height: 1.6;
}

.city-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.city-image {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.city-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.city-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.city-image h4 {
    margin: 15px 15px 10px;
    color: #333;
    font-size: 18px;
}

.city-image p {
    margin: 0 15px 15px;
    color: #666;
    line-height: 1.5;
}

/* City Products Grid */
.city-products-grid {
    display: grid;
    gap: 20px;
    margin: 20px 0;
}

.city-products-grid.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.city-products-grid.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.city-products-grid.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.city-products-grid.columns-5 {
    grid-template-columns: repeat(5, 1fr);
}

.city-products-grid.columns-6 {
    grid-template-columns: repeat(6, 1fr);
}

/* City Categories List */
.city-categories-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.city-categories-list li {
    margin: 0;
}

.city-categories-list a {
    display: block;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.city-categories-list a:hover {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
    transform: translateY(-1px);
}

/* Body Classes */
.city-filtered .woocommerce-products-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.city-filtered .woocommerce-result-count {
    font-style: italic;
    color: #666;
}

/* Loading States */
.city-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.city-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error States */
.city-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px 0;
}

.city-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 5px;
    margin: 20px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .city-selector-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .city-selector {
        min-width: auto;
        width: 100%;
    }
    
    .city-hero-content h1 {
        font-size: 2em;
    }
    
    .city-gallery {
        grid-template-columns: 1fr;
    }
    
    .city-products-grid.columns-2,
    .city-products-grid.columns-3,
    .city-products-grid.columns-4,
    .city-products-grid.columns-5,
    .city-products-grid.columns-6 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .city-categories-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .city-hero-image {
        height: 200px;
    }
    
    .city-hero-content {
        padding: 20px 15px 15px;
    }
    
    .city-hero-content h1 {
        font-size: 1.5em;
    }
    
    .city-products-grid.columns-2,
    .city-products-grid.columns-3,
    .city-products-grid.columns-4,
    .city-products-grid.columns-5,
    .city-products-grid.columns-6 {
        grid-template-columns: 1fr;
    }
}

/* Print Styles */
@media print {
    .city-selector-wrapper,
    .city-widget-form {
        display: none;
    }
    
    .city-hero-section {
        break-inside: avoid;
    }
}
