/**
 * Glowess City Maps Styles
 */

/* Shortcode Wrapper */
.shortcode-wrapper {
    margin: 0;
    padding: 0;
}

/* Maps Container */
.city-maps-container {
    margin: 20px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.city-map {
    width: 100%;
    min-height: 300px;
    background: #f5f5f5;
    border-radius: 8px;
}

/* Info Window Styles */
.city-map-info {
    max-width: 250px;
    padding: 10px;
}

.city-map-info h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.city-map-info p {
    margin: 0 0 8px 0;
    color: #666;
    line-height: 1.4;
    font-size: 14px;
}

.city-map-info small {
    color: #999;
    font-size: 12px;
}

/* Error and Notice Messages */
.city-maps-error,
.city-maps-notice {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
    font-size: 14px;
}

.city-maps-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.city-maps-notice {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.city-maps-error a,
.city-maps-notice a {
    color: inherit;
    text-decoration: underline;
    font-weight: 600;
}

.city-maps-error a:hover,
.city-maps-notice a:hover {
    text-decoration: none;
}

/* Loading State */
.city-map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: #f8f9fa;
    color: #666;
    font-size: 16px;
}

.city-map-loading:before {
    content: "";
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Map Controls */
.city-map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
}

.city-map-control {
    background: white;
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    margin-bottom: 5px;
    text-align: center;
    padding: 8px;
    font-size: 12px;
    color: #333;
    transition: background-color 0.3s;
}

.city-map-control:hover {
    background: #f5f5f5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .city-maps-container {
        margin: 15px 0;
    }
    
    .city-map {
        min-height: 250px;
    }
    
    .city-map-info {
        max-width: 200px;
        padding: 8px;
    }
    
    .city-map-info h4 {
        font-size: 14px;
    }
    
    .city-map-info p {
        font-size: 13px;
    }
    
    .city-maps-error,
    .city-maps-notice {
        padding: 12px;
        margin: 15px 0;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .city-map {
        min-height: 200px;
    }
    
    .city-map-info {
        max-width: 180px;
        padding: 6px;
    }
    
    .city-map-info h4 {
        font-size: 13px;
        margin-bottom: 6px;
    }
    
    .city-map-info p {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    .city-map-info small {
        font-size: 11px;
    }
}

/* Print Styles */
@media print {
    .city-maps-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .city-map {
        background: white;
        border: 1px solid #ddd;
    }
    
    .city-maps-error,
    .city-maps-notice {
        border: 1px solid #999;
        background: white;
        color: black;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .city-map-info {
        background: #2c2c2c;
        color: #e0e0e0;
    }
    
    .city-map-info h4 {
        color: #ffffff;
    }
    
    .city-map-info p {
        color: #cccccc;
    }
    
    .city-map-info small {
        color: #999999;
    }
    
    .city-map-loading {
        background: #1a1a1a;
        color: #cccccc;
    }
    
    .city-maps-error {
        background: #3d1a1a;
        color: #ffcccc;
        border-color: #5a2d2d;
    }
    
    .city-maps-notice {
        background: #1a2d3d;
        color: #ccddff;
        border-color: #2d4a5a;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .city-maps-container {
        border: 2px solid #000;
    }
    
    .city-map-info {
        border: 1px solid #000;
    }
    
    .city-maps-error,
    .city-maps-notice {
        border: 2px solid #000;
        font-weight: bold;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .city-map-loading:before {
        animation: none;
    }
    
    .city-map-control {
        transition: none;
    }
}
