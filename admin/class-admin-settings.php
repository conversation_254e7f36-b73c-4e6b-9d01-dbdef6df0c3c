<?php
/**
 * Admin Settings Class
 * 
 * Handles plugin admin settings and configuration
 */

if (!defined('ABSPATH')) {
    exit;
}

class Glowess_Admin_Settings {
    
    /**
     * Settings page slug
     */
    const SETTINGS_PAGE = 'glowess-city-ecommerce-settings';
    
    /**
     * Constructor
     */
    public function __construct() {
        // Only add admin hooks if in admin area
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'init_settings'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

            // Add settings link to plugin page
            add_filter('plugin_action_links_' . GLOWESS_CITY_ECOMMERCE_PLUGIN_BASENAME, array($this, 'add_settings_link'));

            // Add admin notices
            add_action('admin_notices', array($this, 'admin_notices'));
        }
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Only add submenu page for settings
        add_submenu_page(
            'edit.php?post_type=city',
            __('Şehir E-ticaret Ayarları', 'glowess-city-ecommerce'),
            __('Ayarlar', 'glowess-city-ecommerce'),
            'manage_options',
            self::SETTINGS_PAGE,
            array($this, 'settings_page')
        );
    }
    
    /**
     * Initialize settings
     */
    public function init_settings() {
        register_setting(
            'glowess_city_ecommerce_settings',
            'glowess_city_ecommerce_settings',
            array($this, 'sanitize_settings')
        );
        
        // General settings section
        add_settings_section(
            'general_settings',
            __('Genel Ayarlar', 'glowess-city-ecommerce'),
            array($this, 'general_settings_callback'),
            self::SETTINGS_PAGE
        );
        
        add_settings_field(
            'enable_city_filter',
            __('Şehir Filtresi', 'glowess-city-ecommerce'),
            array($this, 'enable_city_filter_callback'),
            self::SETTINGS_PAGE,
            'general_settings'
        );
        
        add_settings_field(
            'default_city',
            __('Varsayılan Şehir', 'glowess-city-ecommerce'),
            array($this, 'default_city_callback'),
            self::SETTINGS_PAGE,
            'general_settings'
        );
        
        add_settings_field(
            'show_city_in_product_list',
            __('Ürün Listesinde Şehir Göster', 'glowess-city-ecommerce'),
            array($this, 'show_city_in_product_list_callback'),
            self::SETTINGS_PAGE,
            'general_settings'
        );
        
        // Display settings section
        add_settings_section(
            'display_settings',
            __('Görüntüleme Ayarları', 'glowess-city-ecommerce'),
            array($this, 'display_settings_callback'),
            self::SETTINGS_PAGE
        );
        
        add_settings_field(
            'city_selector_position',
            __('Şehir Seçici Konumu', 'glowess-city-ecommerce'),
            array($this, 'city_selector_position_callback'),
            self::SETTINGS_PAGE,
            'display_settings'
        );
        
        add_settings_field(
            'show_city_hero',
            __('Şehir Hero Bölümü', 'glowess-city-ecommerce'),
            array($this, 'show_city_hero_callback'),
            self::SETTINGS_PAGE,
            'display_settings'
        );
        
        // SEO settings section
        add_settings_section(
            'seo_settings',
            __('SEO Ayarları', 'glowess-city-ecommerce'),
            array($this, 'seo_settings_callback'),
            self::SETTINGS_PAGE
        );
        
        add_settings_field(
            'city_url_structure',
            __('URL Yapısı', 'glowess-city-ecommerce'),
            array($this, 'city_url_structure_callback'),
            self::SETTINGS_PAGE,
            'seo_settings'
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, self::SETTINGS_PAGE) === false) {
            return;
        }
        
        wp_enqueue_style(
            'glowess-city-admin',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            GLOWESS_CITY_ECOMMERCE_VERSION
        );
        
        wp_enqueue_script(
            'glowess-city-admin',
            GLOWESS_CITY_ECOMMERCE_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            GLOWESS_CITY_ECOMMERCE_VERSION,
            true
        );
    }
    
    /**
     * Add settings link to plugin page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('edit.php?post_type=city&page=' . self::SETTINGS_PAGE) . '">' . __('Ayarlar', 'glowess-city-ecommerce') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="glowess-city-settings">
                <form method="post" action="options.php">
                    <?php
                    settings_fields('glowess_city_ecommerce_settings');
                    do_settings_sections(self::SETTINGS_PAGE);
                    submit_button();
                    ?>
                </form>
                
                <div class="glowess-city-sidebar">
                    <div class="postbox">
                        <h3 class="hndle"><?php _e('Hızlı Başlangıç', 'glowess-city-ecommerce'); ?></h3>
                        <div class="inside">
                            <ol>
                                <li><?php _e('Şehirler ekleyin ve görsellerini yükleyin', 'glowess-city-ecommerce'); ?></li>
                                <li><?php _e('Ürünlerinize şehir atayın', 'glowess-city-ecommerce'); ?></li>
                                <li><?php _e('Kategorilerinize şehir atayın', 'glowess-city-ecommerce'); ?></li>
                                <li><?php _e('Frontend\'te şehir parametresi ile test edin', 'glowess-city-ecommerce'); ?></li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="postbox">
                        <h3 class="hndle"><?php _e('Kullanım', 'glowess-city-ecommerce'); ?></h3>
                        <div class="inside">
                            <p><strong><?php _e('URL Parametresi:', 'glowess-city-ecommerce'); ?></strong></p>
                            <p><code>?city=istanbul</code></p>
                            <p><?php _e('Mağaza sayfasında şehir filtrelemesi için kullanın.', 'glowess-city-ecommerce'); ?></p>

                            <p><strong><?php _e('Widget:', 'glowess-city-ecommerce'); ?></strong></p>
                            <p><?php _e('Görünüm > Widget\'lar menüsünden "Şehir Seçici" widget\'ını ekleyin.', 'glowess-city-ecommerce'); ?></p>
                        </div>
                    </div>
                    
                    <div class="postbox">
                        <h3 class="hndle"><?php _e('API Endpoints', 'glowess-city-ecommerce'); ?></h3>
                        <div class="inside">
                            <p><code>/wp-json/glowess-city/v1/city/{slug}/products</code></p>
                            <p><code>/wp-json/glowess-city/v1/city/{slug}/categories</code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .glowess-city-settings {
            display: flex;
            gap: 20px;
        }
        .glowess-city-settings form {
            flex: 2;
        }
        .glowess-city-sidebar {
            flex: 1;
        }
        .glowess-city-sidebar .postbox {
            margin-bottom: 20px;
        }
        .glowess-city-sidebar .inside {
            padding: 12px;
        }
        .glowess-city-sidebar ol {
            padding-left: 20px;
        }
        .glowess-city-sidebar code {
            background: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
        }
        </style>
        <?php
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        $sanitized['enable_city_filter'] = isset($input['enable_city_filter']) ? 'yes' : 'no';
        $sanitized['default_city'] = isset($input['default_city']) ? intval($input['default_city']) : '';
        $sanitized['show_city_in_product_list'] = isset($input['show_city_in_product_list']) ? 'yes' : 'no';
        $sanitized['city_selector_position'] = isset($input['city_selector_position']) ? sanitize_text_field($input['city_selector_position']) : 'before_loop';
        $sanitized['show_city_hero'] = isset($input['show_city_hero']) ? 'yes' : 'no';
        $sanitized['city_url_structure'] = isset($input['city_url_structure']) ? sanitize_text_field($input['city_url_structure']) : 'parameter';
        
        return $sanitized;
    }
    
    /**
     * Get settings
     */
    public function get_settings() {
        $defaults = array(
            'enable_city_filter' => 'yes',
            'default_city' => '',
            'show_city_in_product_list' => 'yes',
            'city_selector_position' => 'before_loop',
            'show_city_hero' => 'yes',
            'city_url_structure' => 'parameter'
        );
        
        $settings = get_option('glowess_city_ecommerce_settings', $defaults);
        return wp_parse_args($settings, $defaults);
    }
    
    /**
     * General settings section callback
     */
    public function general_settings_callback() {
        echo '<p>' . __('Şehir e-ticaret sisteminin genel ayarları.', 'glowess-city-ecommerce') . '</p>';
    }
    
    /**
     * Enable city filter callback
     */
    public function enable_city_filter_callback() {
        $settings = $this->get_settings();
        ?>
        <label>
            <input type="checkbox" name="glowess_city_ecommerce_settings[enable_city_filter]" value="yes" <?php checked($settings['enable_city_filter'], 'yes'); ?> />
            <?php _e('Şehir filtresini etkinleştir', 'glowess-city-ecommerce'); ?>
        </label>
        <p class="description"><?php _e('Mağaza sayfalarında şehir filtresini gösterir.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * Default city callback
     */
    public function default_city_callback() {
        $settings = $this->get_settings();
        $cities = get_posts(array(
            'post_type' => 'city',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        ?>
        <select name="glowess_city_ecommerce_settings[default_city]">
            <option value=""><?php _e('Varsayılan şehir yok', 'glowess-city-ecommerce'); ?></option>
            <?php foreach ($cities as $city) : ?>
                <option value="<?php echo $city->ID; ?>" <?php selected($settings['default_city'], $city->ID); ?>>
                    <?php echo esc_html($city->post_title); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <p class="description"><?php _e('Şehir seçilmediğinde gösterilecek varsayılan şehir.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * Show city in product list callback
     */
    public function show_city_in_product_list_callback() {
        $settings = $this->get_settings();
        ?>
        <label>
            <input type="checkbox" name="glowess_city_ecommerce_settings[show_city_in_product_list]" value="yes" <?php checked($settings['show_city_in_product_list'], 'yes'); ?> />
            <?php _e('Ürün listelerinde şehir bilgisini göster', 'glowess-city-ecommerce'); ?>
        </label>
        <p class="description"><?php _e('Mağaza ve kategori sayfalarında ürünlerin altında şehir bilgisini gösterir.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * Display settings section callback
     */
    public function display_settings_callback() {
        echo '<p>' . __('Şehir bilgilerinin nasıl görüntüleneceğini ayarlayın.', 'glowess-city-ecommerce') . '</p>';
    }
    
    /**
     * City selector position callback
     */
    public function city_selector_position_callback() {
        $settings = $this->get_settings();
        $positions = array(
            'before_loop' => __('Ürün listesi öncesi', 'glowess-city-ecommerce'),
            'after_loop' => __('Ürün listesi sonrası', 'glowess-city-ecommerce'),
            'sidebar' => __('Sadece widget olarak', 'glowess-city-ecommerce')
        );
        ?>
        <select name="glowess_city_ecommerce_settings[city_selector_position]">
            <?php foreach ($positions as $value => $label) : ?>
                <option value="<?php echo esc_attr($value); ?>" <?php selected($settings['city_selector_position'], $value); ?>>
                    <?php echo esc_html($label); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <p class="description"><?php _e('Şehir seçicinin mağaza sayfalarında nerede gösterileceğini belirler.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * Show city hero callback
     */
    public function show_city_hero_callback() {
        $settings = $this->get_settings();
        ?>
        <label>
            <input type="checkbox" name="glowess_city_ecommerce_settings[show_city_hero]" value="yes" <?php checked($settings['show_city_hero'], 'yes'); ?> />
            <?php _e('Şehir seçildiğinde hero bölümünü göster', 'glowess-city-ecommerce'); ?>
        </label>
        <p class="description"><?php _e('Şehir seçildiğinde sayfanın üstünde şehir bilgileri ve görsellerini gösterir.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * SEO settings section callback
     */
    public function seo_settings_callback() {
        echo '<p>' . __('Arama motoru optimizasyonu ayarları.', 'glowess-city-ecommerce') . '</p>';
    }
    
    /**
     * City URL structure callback
     */
    public function city_url_structure_callback() {
        $settings = $this->get_settings();
        $structures = array(
            'parameter' => __('URL Parametresi (?city=istanbul)', 'glowess-city-ecommerce'),
            'path' => __('URL Yolu (/istanbul/) - Gelecek sürümde', 'glowess-city-ecommerce')
        );
        ?>
        <select name="glowess_city_ecommerce_settings[city_url_structure]">
            <?php foreach ($structures as $value => $label) : ?>
                <option value="<?php echo esc_attr($value); ?>" <?php selected($settings['city_url_structure'], $value); ?> <?php disabled($value, 'path'); ?>>
                    <?php echo esc_html($label); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <p class="description"><?php _e('Şehir bilgisinin URL\'de nasıl gösterileceğini belirler.', 'glowess-city-ecommerce'); ?></p>
        <?php
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            ?>
            <div class="notice notice-error">
                <p>
                    <?php 
                    echo sprintf(
                        __('Glowess City E-commerce requires %s to be installed and active.', 'glowess-city-ecommerce'),
                        '<strong>WooCommerce</strong>'
                    );
                    ?>
                </p>
            </div>
            <?php
        }
        
        // Show setup notice for new installations
        if (!get_option('glowess_city_ecommerce_setup_complete')) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p>
                    <?php _e('Glowess City E-commerce kurulumu tamamlandı!', 'glowess-city-ecommerce'); ?>
                    <a href="<?php echo admin_url('post-new.php?post_type=city'); ?>"><?php _e('İlk şehrinizi ekleyin', 'glowess-city-ecommerce'); ?></a>
                    <?php _e('veya', 'glowess-city-ecommerce'); ?>
                    <a href="<?php echo admin_url('edit.php?post_type=city&page=' . self::SETTINGS_PAGE); ?>"><?php _e('ayarları yapılandırın', 'glowess-city-ecommerce'); ?></a>.
                </p>
            </div>
            <?php
            update_option('glowess_city_ecommerce_setup_complete', true);
        }
    }
}
